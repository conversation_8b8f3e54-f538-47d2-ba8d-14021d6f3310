<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/items/codi-task')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Item Pembekal (Codification Task)<br>
                <small>Masukkan Dokumen Number (RNC), Nama Item, Identification No (ICNO)  pada carian diatas...
                <br />
                <strong>Senarai Item yang ditugaskan kepada Codification Team mengikut status</strong>
                </small>
            </h1>
        </div>
    </div>

    
    
    <div class="block">
        <div class="block-title">
            <h2><strong>Carian</strong></h2>
            <div class="block-options pull-right action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="<?php echo e(url('/support/report/log/item-codification')); ?>/<?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?>" 
                   data-title="List Action Today ">View Today Action</a>
            </div>
        </div>

        <div class="block">

            <?php if($errors->any()): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>
            <form id="form-search-mminf" action="<?php echo e(url("/find/items/codi-task")); ?>" method="post" class="form-horizontal" onsubmit="return true;">
                <?php echo e(csrf_field()); ?>

                <input name="_method" id="_method"  type="hidden" value="POST">

                <div class="form-group">
                    <label class="col-md-3 control-label" for="date_start">Carian </label>

                    <div class="col-md-5">
                        <input type="text" id="cari" name="cari" class="form-control" 
                                placeholder="Identification No. " 
                                value="<?php if(old('cari') != null): ?><?php echo e(old('cari')); ?><?php else: ?><?php echo e($carian); ?><?php endif; ?>">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="date_start">Tarikh </label>

                    <div class="col-md-5">
                        <input type="text" id="tarikh" name="tarikh" class="form-control input-datepicker-close" 
                                data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                                value="<?php if(old('tarikh') != null): ?><?php echo e(old('tarikh')); ?><?php else: ?><?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?><?php endif; ?>">
                    </div>
                </div>

                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                        <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

    <?php if($listdata == null || count($listdata) == 0): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                      <?php if(isset($error)): ?>
                      <p>Carian mesti lebih 3 aksara!</p>
                      <?php else: ?>
                      <p>Tidak dijumpai!</p>
                      <?php endif; ?>
                  </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Item Pembekal  (Codification Task)</strong>
                        <small>Hasil Carian : <?php echo e($carian); ?></small>
                    </h1>
                    
                </div>

                <div class="table-responsive">
                    <table id="item-codification-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">TARIKH</th>
                            <th class="text-center">IC NO.</th>
                            <th class="text-center">NAME</th>
                            <th class="text-center">DOC_NO</th>
                            <th class="text-center">PRODUCT_NAME</th>
                            <th class="text-center">COMPANY_NAME</th>
                            <th class="text-center">EP_NO</th>
                            <th class="text-center">STATUS</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-left"><?php echo e($data->created_date_status); ?></td>
                                <td class="text-left"><?php echo e($data->identification_no); ?></td>
                                <td class="text-left"><?php echo e($data->name); ?></td>
                                <td class="text-left"><?php echo e($data->doc_no); ?></td>
                                <td class="text-left"><?php echo e($data->item_name); ?></td>
                                <td class="text-left"><?php echo e($data->company_name); ?></td>
                                <td class="text-left"><?php echo e($data->ep_no); ?></td>
                                <td class="text-left"><?php echo e($data->status_name); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
       
    <?php endif; ?>

    <?php echo $__env->make('_shared._modalListLogAction', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    
</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_item_codi_task.blade.php ENDPATH**/ ?>