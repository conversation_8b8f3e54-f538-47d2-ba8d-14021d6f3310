<?php
/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 2/27/2018
 * Time: 12:46 PM
 */

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Models\EpSupportMonitoringQtEp;
use App\Services\Traits\SupplierService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SSHService;
use Carbon\Carbon;
use Illuminate\Support\Facades\SSH;

class DashboardController extends Controller
{
    use SupplierService;
    use OSBService;
    use ProfileService;
    use SSHService;
    use FulfilmentService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function getDashboardGfmas(){
        $serviceCode = 'GFM-010%';
//        $list = $this->getDashboardGfmasAll($serviceCode);
        return $this->getDashboardDetail();
    }

    public function getDashboardDetail() {
        return view('dashboard_gfmas', []);
    }
    
    public function getDashboardApiveOutbound() {
        $dataGfm010 = $this->getDashboardGfmasByServiceCode('GFM-010%');
        $apiveFiles = $this->getListAPIVEOutFolder();
        $totalApiveOutPending = count($apiveFiles);
        if ($dataGfm010) {
            $dataGfm010[0]->totalPercent = round($dataGfm010[0]->apive_1gfmas / 999 * 100);
            //$dataGfm010[0]->totalPercent = 52;
            if ($dataGfm010[0]->totalPercent > 50 && $dataGfm010[0]->totalPercent < 90) {
                $chartColor = '#f39c12';
            } elseif ($dataGfm010[0]->totalPercent >= 90) {
                $chartColor = '#c0392b';
            } else {
                $chartColor = '#2ecc71';
            }
            $dataGfm010[0]->chartColor = $chartColor;

            $dataGfm010[0]->dates = Carbon::parse($dataGfm010[0]->dates)->format('d/m/Y');
        } else {
            $dataGfm010[] = (object)[
                'service_code' => 'GFM-370',
                'trans_type' => '-',
                'dates' => '-',
                'apive_1gfmas' => 0,
                'totalPercent' => 0,
                'chartColor' => '#fff',
            ];
        }
        
        $html = "
            <!-- Dashboard Info Title -->
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    1GFMAS MasterDataVendor APIVE <strong>(Outbound)</strong>
                </h5>
            </div>
            <!-- END Dashboard Info Title -->

            <!-- Dashboard Info -->
            <div class='block-section text-center' style='padding-top: 20px;'>
                <div class='pie-chart block-section' data-percent='{$dataGfm010[0]->totalPercent}'
                     data-size='90' data-line-width='2'
                     data-bar-color='{$dataGfm010[0]->chartColor}' data-track-color='#ffffff'>
                    <span>{$dataGfm010[0]->totalPercent}%</span>
                </div>
            </div>
            <table class='table table-borderless table-striped table-vcenter'>
                <tbody>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Service Code</strong></td>
                    <td><strong>{$dataGfm010[0]->service_code}</strong></td>
                </tr>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Transaction Type</strong></td>
                    <td>{$dataGfm010[0]->trans_type}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Dates</strong></td>
                    <td>{$dataGfm010[0]->dates}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Transactions</strong></td>
                    <td><strong>{$dataGfm010[0]->apive_1gfmas}</strong> /999</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Pending (OUT Folder)</strong></td>
                    <td><strong><a href='#modal-list-data' 
                        class='modal-list-data-action label label-danger' 
                        data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-apive-outbound'
                        data-title='List Pending Transactions APIVE' >{$totalApiveOutPending}</a></strong></td>
                </tr>
                </tbody>
            </table>
            <!-- END Dashboard Info -->";

        return $html;
        
    }

    public function getDashboardApoveInbound() {
        $dataGfm370 = $this->getDashboardGfmasByServiceCode('GFM-370%');
        $apoveFiles = $this->getListAPOVEInFolder();
        $totalApoveInPending = count($apoveFiles);
        if ($dataGfm370) {
            $dataGfm370[0]->dates = Carbon::parse($dataGfm370[0]->dates)->format('d/m/Y');
        } else {
            $dataGfm370[] = (object)[
                'service_code' => 'GFM-370',
                'trans_type' => '-',
                'dates' => '-',
                'apive_1gfmas' => 0,
            ];
        }

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        1GFMAS MasterDataVendor APOVE <strong>(Inbound)</strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Service Code</strong></td>
                        <td><strong>{$dataGfm370[0]->service_code}</strong></td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Transaction Type</strong></td>
                        <td>{$dataGfm370[0]->trans_type}</td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>Dates</strong></td>
                        <td>{$dataGfm370[0]->dates}</td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>Transactions</strong></td>
                        <td><strong>{$dataGfm370[0]->apive_1gfmas}</strong></td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>Pending (IN Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-apove-inbound'
                            data-title='List Pending Transactions APOVE' >{$totalApoveInPending}</a></strong></td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }
    
    public function getDashboard1GfmasOutbound() {
        $outboundFiles = $this->getList1GfmasFolderOUT();
        $totalOutboundFilesPending = count($outboundFiles);
        
        $inbound1GFMASServerFiles = $this->getList1GfmasServerFolderIN();
        $inbound1GFMASServerFilesPending = count($inbound1GFMASServerFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        1GFMAS Monitoring <strong>(OUTBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT eP Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-outbound'
                            data-title='List Pending Files (OUT eP FOLDER)' >{$totalOutboundFilesPending}</a></strong></td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (IN 1GFMAS Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/1gfmas-inbound'
                            data-title='List Pending Files (IN 1GFMAS  FOLDER)' >{$inbound1GFMASServerFilesPending}</a></strong></td>
                    </tr>
                    
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;

    }

    public function getDashboard1GfmasInbound() {
        $inboundFiles = $this->getList1GfmasFolderIN();
        $totalinboundFilesPending = count($inboundFiles);
        
        $outbound1GFMASServerFiles = $this->getList1GfmasServerFolderOUT();
        $outbound1GFMASServerFilesPending = count($outbound1GFMASServerFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        1GFMAS Monitoring <strong>(INBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT 1GFMAS Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/1gfmas-outbound'
                            data-title='List Pending Files (OUT 1GFMAS FOLDER)' >{$outbound1GFMASServerFilesPending}</a></strong></td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (IN eP FOLDER)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-inbound'
                            data-title='List Pending Files (IN eP FOLDER)' >{$totalinboundFilesPending}</a></strong></td>
                    </tr>
                    
                    
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;

    }
    
    public function getDashboardQuartz() {
        $dataFired = $this->getDashboardQuartzFired();
        $dataExecution = $this->getDashboardQuartzExecution();

        if ($dataFired) {
            $dataFired[0]->prev_fired = Carbon::parse($dataFired[0]->prev_fired)->format('d/m/Y H:i:s A');
            $dataFired[0]->next_fired = Carbon::parse($dataFired[0]->next_fired)->format('d/m/Y H:i:s A');
            if($dataFired[0]->trigger_state === 'WAITING') {
                $dataFired[0]->state_color = 'label-warning';
            } elseif ($dataFired[0]->trigger_state === 'BLOCKED') {
                $dataFired[0]->state_color = 'label-danger';
            } else {
                $dataFired[0]->state_color = 'label-info';
            }
        }

        if ($dataExecution) {
            $dataExecution[0]->finish_execution_date = Carbon::parse($dataExecution[0]->finish_execution_date)->format('d/m/Y H:i:s A');
        }
        $html = '';
        if(count($dataFired) > 0){
        $html = "
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    Quartz <strong>Log</strong>
                </h5>
            </div>
            <!-- END Dashboard Info Title -->

            <!-- Dashboard Info -->
            <table class='table table-borderless table-striped table-vcenter'>
                <tbody>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Service Code</strong></td>
                    <td><strong>{$dataFired[0]->job_group}</strong></td>
                </tr>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Transaction Type</strong></td>
                    <td>{$dataFired[0]->job_name}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Trigger State</strong></td>
                    <td><span class='label {$dataFired[0]->state_color}'>{$dataFired[0]->trigger_state}</span></td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Previous Fired</strong></td>
                    <td>{$dataFired[0]->prev_fired}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Next Fire</strong></td>
                    <td>{$dataFired[0]->next_fired}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Finish Execution Date</strong></td>
                    <td>{$dataExecution[0]->finish_execution_date}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Duration</strong></td>
                    <td>{$dataExecution[0]->duration}</td>
                </tr>
                </tbody>
            </table>
            <!-- END Dashboard Info -->";
        }
        return $html;
    }

    public function getDashboardDiInterfaceLogApive() {
        $data = $this->getDashboardDiInterfaceLog();

        if ($data) {

        } else {

        }

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Di Interface Log - <strong>APIVE (Last Execute)</strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Start Time</strong></td>
                        <td>{$data[0]->data_start_time}</td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>End Time</strong></td>
                        <td>{$data[0]->data_end_time}</td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>File Name</strong></td>
                        <td>{$data[0]->file_name}</td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;

    }

    public function checkConnectionGFMAS(){
        $data = array();
        $commands  = [
            "echo 'exit' | sftp -oPort=2022 eperolehan@10.38.206.73",
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            //var_dump($result);
            array_push($data,trim($result));
        });

        $telnet = "";
        foreach ($data as $d) {
            $telnet .= $d."<br />";
        }

        $html = "<div><strong>{$telnet}</strong></div>";

        return $html;
    }

    public function checkQtMonitoring(){
        $listQtPublished = $this->getListQtPublished();
        $totalQtPublished = count($listQtPublished);
        $listQtClosing = $this->getListQtClosing();
        $totalQtClosing = count($listQtClosing);
        
        $totalAccumulatedQtPublished = EpSupportMonitoringQtEp::where('qt_monitor_type','QT_PUBLISHED')
                ->whereDate('qt_date',Carbon::now()->format('Y-m-d'))
                ->count();
        
        $totalAccumulatedQtClosing = EpSupportMonitoringQtEp::where('qt_monitor_type','QT_CLOSING')
                ->whereDate('qt_date',Carbon::now()->format('Y-m-d'))
                ->count();
        
        
        $html = "";

        $html .= "<div class='row text-center'>";
        $html .= "<div class='col-xs-6'>
                    <h3>
                    <strong>$totalQtPublished</strong> <small>/$totalAccumulatedQtPublished</small><br>
                        <small><i class='fa fa-thumbs-up'></i> Published</small>
                    </h3>
                </div>";
        $html .= "<div class='col-xs-'>
                    <h3>
                        <strong>$totalQtClosing</strong> <small>/$totalAccumulatedQtClosing</small><br>
                        <small><i class='fa fa-power-off'></i> Closing</small>
                    </h3>
                </div>";
        $html .= "</div>";


        return $html;
    }
    
    public function checkEjbOsb(){
        $list = $this->getDashboardEjbOsb();
//        dd($list);
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Trans Date</th>
                        <th>Total Invoke</th>
                        <th>No EJB Receiver</th>
                        <th>EJB Timeout</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            if($data->no_ejb_receiver > 0) {
                $data->no_ejb_receiver = "<a href='/find/1gfmas/ws/log/checkWsOsbEjbNoReceiver' target='_blank' ><span class='badge label-danger'><strong>$data->no_ejb_receiver</strong></span></a>";
            }
            if($data->ejb_timeout > 0) {
                $data->ejb_timeout = "<a href='/find/1gfmas/ws/log/checkWsOsbEjbTimeOut' target='_blank' ><span class='badge label-danger'><strong>$data->ejb_timeout</strong></span></a>";
            }
            $html .= "
            <tr>
                <td style='width: 30%;'><strong>$data->trans_date</strong></td>
                <td class='text-center'>$data->total_invoke</td>
                <td class='text-center'>$data->no_ejb_receiver</td>
                <td class='text-center'>$data->ejb_timeout</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkWsValidationException(){
        $list = $this->getDashboardStatisticWsValidationException();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Transaction Date</th>
                        <th>Total Records</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $data->total_desc = "<a href='/find/1gfmas/ws/log/checkWsValidation' target='_blank' ><span class='badge label-danger'><strong>$data->total</strong></span></a>";

            $html .= "
            <tr>
                <td style='width: 60%;' class='text-center'><strong>$data->trans_date</strong></td>
                <td class='text-center'>$data->total_desc</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }
    
    
    
    public function checkOsbRetry(){
        $list = $this->getDashboardOsbRetry();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Target System</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->service_name</strong></td>
                <td>$data->target_system</td>
                <td class='text-center'><span class='badge label-danger'>$data->counts</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }
    
    public function checkOsbNotifyRetry(){
        $list = $this->getDashboardOsbNotifyRetry();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Target System</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->service_name</strong></td>
                <td>$data->target_system</td>
                <td class='text-center'><span class='badge label-danger'>$data->counts</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }
    
    public function checkOsbBatchRetry(){
        $list = $this->getDashboardOsbBatchRetry();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Code</th>
                        <th>Target System</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->service_code</strong></td>
                <td>$data->target_system</td>
                <td class='text-center'><span class='badge label-danger'>$data->counts</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }
    
    public function checkFileErrorInbound(){
        $list = $this->getListStatisticFileErrProcessing();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Trans Date</th>
                        <th>Service Code</th>
                        <th>Service Name</th>
                        <th>Total Files</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            if($data->total > 0) {
                $data->total = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/batch/ep-file-error-inbound/$data->service_code/$data->trans_date'
                            data-title='List Pending Files (OUT 1GFMAS FOLDER)' >{$data->total}</a>";
            }
            $html .= "
            <tr>
                <td style='width: 30%;'><strong>$data->trans_date</strong></td>
                <td class='text-center'>$data->service_code</td>
                <td class='text-center'>$data->service_name</td>
                <td class='text-center'><strong>$data->total</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }
    
    public function checkWsItemCodeErrorInGFM100(){
        $list = $this->getDashboardStatisticWsItemCodeInGFM100();
        $html = "";
        
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Transaction Date</th>
                        <th>Error 'Tidak Wujud' </th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $data->total_desc = "<a href='/find/1gfmas/ws/log/checkWsItemCodeErrorInGFM100' target='_blank' ><span class='badge label-danger'><strong>$data->total</strong></span></a>";

            $html .= "
            <tr>
                <td  class='text-left'><strong>$data->trans_date</strong></td>
                <td style='width: 40%;' class='text-left'>$data->error_type</td>
                <td class='text-center'>$data->total_desc</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }
    
    public function checkWsItemCodeErrorInMMINF(){
        $list = $this->getDashboardStatisticWsItemCodeInMMINF();
        $html = "";
        
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Transaction Date</th>
                        <th>Error Type</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $data->total_desc = "<a href='/find/1gfmas/ws/log/checkWsItemCodeErrorInMMINF' target='_blank' ><span class='badge label-danger'><strong>$data->total</strong></span></a>";
            
            $typeError = $data->error_type;
            $type = '';
            
            if(strpos($typeError, 'Maklumat') !== false){
                $type = 'CRE : Item sudah wujud';
            }else if(strpos($typeError, 'KOD ITEM') !== false){
                $type = 'Locked! Cuba semula';
            }else if(strpos($typeError, 'Kemaskin') !== false){
                $type = 'ADJ : Item tidak wujud';
            }else{
                $type = $data->error_type;
            }
            
            $html .= "
            <tr>
                <td  class='text-left'><strong>$data->trans_date</strong></td>
                <td style='width: 40%;' class='text-left'>$type</td>
                <td class='text-center'>$data->total_desc</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }
}