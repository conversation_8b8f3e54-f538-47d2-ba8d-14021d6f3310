<?php

namespace App\Http\Controllers;

class AppSchedulerController extends Controller {  

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }        

    
    public function getSIT(){
        return $this->getAppSchedulerSIT();
    }

    public function getAppSchedulerSIT() {
        return view('list_app_scheduler_sit', []);
    }
    
    public function getProd(){
        return $this->getAppSchedulerProd();
    }
    
     public function getAppSchedulerProd() {
        return view('list_app_scheduler_prod', []);
    }

    


}
