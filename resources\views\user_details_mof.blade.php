@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find')}}/{{$type}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    @if ($listdata == null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    @if($result == 'notfound')
                        <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                        <small>Rekod tidak dijumpai!</small>
                    @else
                        <i class="gi gi-search"></i>Carian @if($type == 'mofno')MOF No. @else eP No. @endif<br>
                        <small><PERSON><PERSON><PERSON><PERSON> @if($type == 'mofno')MOF No. @else eP No. @endif pada carian diatas...</small>
                    @endif
                </h1>
            </div>
        </div>
    @endif

    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title  panel-heading" style = "background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>ePerolehan (Pembekal) : {{ $listdata[0]->company_name }}</strong></h1>
                </div>
                <div class="row">
                    @if ($isNotAppointedAdmin == true)
                    <div class="col-sm-12">
                        <div class="notify-alert alert alert-warning alert-dismissable">
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                            <h4><i class="fa fa-exclamation-triangle"></i> Admin Pembekal masih belum dilantik!</h4> 
                        </div>
                    </div>
                    @endif
                    <div class="col-sm-3">
                        <div class="block">
                            
                            <div class="block-title">
                                <h2>Maklumat Syarikat  </h2>
                            </div>
 
                            <h6><strong>{{ $listdata[0]->company_name }}</strong></h6>
                            <address>
                                <strong>Supplier ID </strong> : {{ $listdata[0]->supplier_id }}<br />
                                <strong>Appl ID </strong> : {{ $listdata[0]->latest_appl_id }}<br />
                                <strong>Business Type </strong> : {{ $listdata[0]->business_type }}  &raquo;  ({{ App\Services\EPService::$BUSINESS_TYPE[$listdata[0]->business_type] }})<br />
                                <strong>SSM No </strong> : {{ $listdata[0]->reg_no }}<br />
                                <strong>eP No </strong> : {{ $listdata[0]->ep_no }}  <a target="_blank" href="{{ url('/find/gfmas/apive/') }}/{{ $listdata[0]->ep_no }}" >Check Apive</a><br />
                                <strong>Establish Date </strong> : {{ $listdata[0]->establish_date }} <br />
                                <strong>Last Date Changed </strong> : {{ $listdata[0]->s_changed_date }} <br />
                                
                                <strong>Record Status </strong> : <span class="bolder">{{ $listdata[0]->s_record_status }} </span><br />
                                
                                @if($basicCompInfo)
                                <strong>Address </strong> : ( Address ID: {{ $basicCompInfo->address_id }} )<br />
                                {{ $basicCompInfo->address_1 }}<br />
                                {{ $basicCompInfo->address_2 }} {{ $basicCompInfo->address_3 }}<br />
                                {{ $basicCompInfo->postcode }} {{ $basicCompInfo->city_name }}, {{ $basicCompInfo->district_name }}<br />
                                {{ $basicCompInfo->state_name }}, {{ $basicCompInfo->country_name }}<br />
                                @endif
                                <strong>Phone No: </strong> : {{ $basicCompInfo->phone_country }}{{ $basicCompInfo->phone_area }}{{ $basicCompInfo->phone_no }} <br /> 
                                <strong>Fax No: </strong> : {{ $basicCompInfo->fax_country }}{{ $basicCompInfo->fax_area }}{{ $basicCompInfo->fax_no }} <br /> <br /> 
                                
                                @if($hqGstInfo)
                                    <strong>GST Registration No. : </strong> {{ $hqGstInfo->gst_reg_no }}<br />
                                    <strong>GST Effective Date : </strong> {{ $hqGstInfo->gst_eff_date }}<br /><br />
                                @else
                                    <strong>GST Registration No. : </strong> Not Registered <br /><br />
                                @endif

                                <strong>Total Items Catalogs (Approved & Published) </strong> : <a href="{{ url('find/items/supplier') }}/{{ $listdata[0]->ep_no }}" 
                                                                                                   class="btn btn-info btn-xs"  target="_blank" title="View Detail All Items" >{{ $totalItems }}</a><br />
                                
                                <br />
                                <strong>SAP Vendor Code</strong> : @if($sapVendorCode){{ $sapVendorCode->sap_vendor_code }} @else Tiada @endif<br />
                                
                                <br />
                                <strong>In Progress Application History</strong> : @if(count($listinProgressSuppProcessAppl) > 0)
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#app_progress_{{$listdata[0]->supplier_id }}" >{{ $listinProgressSuppProcessAppl[0]->appl_no }}</span> @else Tiada @endif<br />
                                  
                                    
                                
                                <br />
                                <strong>Payment History</strong> : @if($listSuppPayment)
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#payment_{{$listdata[0]->supplier_id }}" >{{count($listSuppPayment)}}</span> @else Tiada @endif<br />
                                
                               
                            </address>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="block">

                            <div class="block-title">
                                <h2>Maklumat MOF</h2>
                            </div>
 
                            <address>
                                <strong>MOF NO </strong> : {{ $listdata[0]->mof_no }}<br />
                                <strong>Effective Date </strong> : {{ $listdata[0]->ma_eff_date }}<br />
                                <strong>Expired Date </strong> : <span class=" @if($listdata[0]->is_mof_expired) label label-danger @endif">{{ $listdata[0]->ma_exp_date }}</span><br />
                                <strong>Record Status </strong> : {{ $listdata[0]->ma_record_status }}<br />
                                
                                @if($suppMofStatus)
                                <br />
                                <strong>Bumi Status </strong> : {{ $suppMofStatus->bumi_status }}<br />
                                <strong>Registration Type </strong> : {{ $suppMofStatus->type }}<br />
                                @endif
                             
                                
                                <br />
                                <strong>Cert Mof</strong> : <br/>
                                @if($listSuppMofVirtCert && count($listSuppMofVirtCert) > 0)
                                    @foreach ($listSuppMofVirtCert as $cerVirt)
                                        {{ $cerVirt->cert_serial_no  }} ({{ $cerVirt->cert_type  }})  #{{ $cerVirt->appl_id  }}
                                        <a href="{{url('/download/mofcert')}}/{{$cerVirt->cert_serial_no}}" target="_blank" >Download</a> 
                                        @if($cerVirt->appl_id != $listdata[0]->latest_appl_id)
                                        <span class="text-danger">(Data Fix!) Appl ID tidak sama</span>@endif
                                        <br/>
                                    @endforeach
                                @else
                                Tiada
                                @endif

                                <br />
                                <strong>List branch</strong> : @if(count($listSupplierBranch) > 0 )
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#branch_{{$listdata[0]->supplier_id }}" >{{count($listSupplierBranch)}}</span> @else Tiada @endif<br />
                                
                                <br />
                                <strong>List Bank</strong> : @if($listSupplierBank)
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#bank_{{$listdata[0]->supplier_id }}" >{{count($listSupplierBank)}}</span>
                                    @foreach($listSupplierBank as $bank)  @if($bank->bank_status == 9) <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;"></i> @break @endif @endforeach
                                @else Tiada @endif<br />

                                <br />
                                <strong>List Category Code</strong> : @if(count($listSupplierCategoryCode) > 0 )
                                    <a href="#modal-category-code" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierCategoryCode)}}</a> @else Tiada @endif<br />
                                <br />
                                <strong>List Pending Transaction</strong> : @if ($isPendingTransaction == true)
                                    <span  class="btn btn-danger btn-xs " data-toggle="collapse" data-target="#pendingtransact_{{$listdata[0]->supplier_id }}" <i class="gi gi-circle_exclamation_mark " style="font-size: 10pt;"></i> Details</span>@else Tiada @endif<br />
                                 <br />


                            </address>
                        </div>
                    </div>
                    @if($basicCompInfo)
                    <div class="col-sm-3">
                        <div class="block">

                            <div class="block-title">
                                <h2>Business Network</h2>
                            </div>
 
                            <address>
                                <strong>Federal? </strong> : {{ $basicCompInfo->is_with_federal  }}<br />
                                <strong>State? </strong> : {{ $basicCompInfo->is_with_state  }}<br />
                                <strong>Local Council? </strong> : {{ $basicCompInfo->is_with_statutory  }}<br />
                                <strong>GLC? </strong> : {{ $basicCompInfo->is_with_glc  }}<br />
                                <strong>Others? </strong> : {{ $basicCompInfo->is_with_others  }}<br />
                            </address>
                        </div>
                    </div>
                    @endif
                    
                    @if(count($listWorkFlow) > 0)
                    <div class="col-sm-3">
                        <div class="block">

                            <div class="block-title">
                                <h2>Application Status</h2>
                            </div>
                            @if(count($listWorkFlow)>0)
                            <strong>Appl ID</strong> : {{ $listWorkFlow[0]->appl_id  }}<br />
                            <strong>Appl No</strong> : {{ $listWorkFlow[0]->appl_no  }}<br />
                            <strong>Appl Record  Status</strong> : {{ App\Services\EPService::$RECORD_STATUS[$listWorkFlow[0]->record_status]  }} &nbsp;
                            @if($listWorkFlow[0]->record_status == 9) <i class="gi gi-circle_exclamation_mark text-danger" title="Ask Technical to review this issue!" style="font-size: 10pt;"></i>@endif<br /><br />
                            @endif
                            @foreach ($listWorkFlow as $datawf)
                            <address>
                                <strong>Is Current</strong> : {{ $datawf->is_current  }} &nbsp; , &nbsp; 
                                <strong>Status </strong> : {{ $datawf->wf_status  }}<br />
                                <strong>Created Date</strong> : {{ $datawf->wf_created_date  }}<br />
                            </address>
                            @endforeach
                            
                            
                            @if(isset($listRemarksCancelReject) && count($listRemarksCancelReject) > 0)
                            <strong><span class="bolder">Remarks </span></strong><br />
                            @foreach($listRemarksCancelReject as $objRemark)
                            <strong>{{$objRemark->doc_type}}</strong> : <span style="font-style: italic">{{ $objRemark->remark  }}</span>  <br/>
                            @endforeach
                            <br/><br/>
                            @endif
                            
                            @if(isset($listAttachmentCancelReject) && count($listAttachmentCancelReject) > 0)
                            <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                            @foreach($listAttachmentCancelReject as $objAttReject)
                            <strong><a href="{{url('/download/attachment/cancel-reject/')}}/{{$objAttReject->attachment_id}}" target="_blank" >{{$objAttReject->doc_type}} - {{$objAttReject->file_name}}</a> </strong>
                            @endforeach
                            <br/><br/>
                            @endif
                            
                            
                        </div>
                    </div>
                    @endif
                </div>
                
                <div id= "app_progress_{{$listdata[0]->supplier_id }}" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>In Progress Application History: (@if(count($listinProgressSuppProcessAppl) > 0){{ $listinProgressSuppProcessAppl[0]->appl_no }}@endif)</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable1" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Appl Type</th>
                                            <th class="text-center">Appl No.</th>
                                            <th class="text-center">Is Active Appl</th>
                                            <th class="text-center">Appl Created By</th>
                                            <th class="text-center">Appl Changed Date</th>
                                            <th class="text-center">Is Resubmit</th>
                                            <th class="text-center">WF Created Date</th>
                                            <th class="text-center">WF Changed Date</th>
                                            <th class="text-center">WF Status</th>
                                            <th class="text-center">WF Is Current</th>
                                            
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach ($listinProgressSuppProcessAppl as  $indexKey => $data)   
                                        <tr>
                                            <td class="text-center">{{ ++$indexKey }}</td>
                                            <td class="text-center">{{ $data->appl_type }}</td>
                                            <td class="text-center">{{ $data->appl_no }}</td>
                                            <td class="text-center">{{ $data->is_active_appl }}</td>
                                            <td class="text-center">{{ $data->appl_created_by }}</td>
                                            <td class="text-center">{{ $data->changed_date }}</td>
                                            <td class="text-center">{{ $data->is_resubmit }}</td>
                                            <td class="text-center">{{ $data->wf_created_date }}</td>
                                            <td class="text-center">{{ $data->wf_changed_date }}</td>
                                            <td class="text-center">{{ $data->wf_status }}</td>
                                            <td class="text-center">{{ $data->is_current }}</td>
                                            
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                
                <div id= "payment_{{$listdata[0]->supplier_id }}" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>Payment History : {{ count($listSuppPayment) }}</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable2" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Bill No.</th>
                                            <th class="text-center">Bill Type</th>
                                            <th class="text-center">Bill Date</th>
                                            <th class="text-center">Bill Amount</th>
                                            <th class="text-center">Order ID</th>
                                            <th class="text-center">Payment Date</th>
                                            <th class="text-center">Payment Mode</th>
                                            <th class="text-center">Status</th>
                                            <th class="text-center">Receipt No.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach ($listSuppPayment as  $indexKey => $data)   
                                        <tr>
                                            <td class="text-center">{{ ++$indexKey }}</td>
                                            <td class="text-center">{{ $data->bill_no }}</td>
                                            <td class="text-center">{{ $data->bill_type }}</td>
                                            <td class="text-center">{{ $data->bill_date }}</td>
                                            <td class="text-center">{{ $data->payment_amt }}</td>
                                            <td class="text-center">{{ $data->payment_id }} - {{ $data->payment_gateway }}</td>
                                            <td class="text-center">{{ $data->payment_date }}</td>
                                            <td class="text-center">{{ $data->payment_mode }}</td>
                                            <td class="text-center">{{ $data->status }}</td>
                                            <td class="text-center">{{ $data->receipt_no }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id= "branch_{{$listdata[0]->supplier_id }}" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>List Branch: @if(count($listSupplierBranch) > 0){{ count($listSupplierBranch) }}@endif</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable3" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Branch Name</th>
                                            <th class="text-center">Branch Code</th>
                                            <th class="text-center">SAP Vendor Code</th>
                                            <th class="text-center">Changed Date</th>
                                            <th class="text-left">GST</th>
                                            <th class="text-center">Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach ($listSupplierBranch as  $indexKey => $data)   
                                        <tr>
                                            <td class="text-center">{{ ++$indexKey }}</td>
                                            <td class="text-center">{{ $data->branch_name }}</td>
                                            <td class="text-center">{{ $data->branch_code }}</td>
                                            <td class="text-center">{{ $data->sap_vendor_code }}</td>
                                            <td class="text-center">{{ $data->changed_date }}</td>
                                            <td class="text-left">
                                                <strong>Registration No. :</strong> {{ $data->gst_reg_no }}<br />
                                                <strong>Effective Date :</strong> {{ $data->gst_eff_date }}<br />
                                            </td>
                                            <td class="text-center">
                                                {{ $data->address_1 }},
                                                {{ $data->address_2 }},
                                                {{ $data->address_3 }}<br />
                                                {{ $data->postcode }} {{ $data->city_name }}, {{ $data->district_name }}<br />
                                                {{ $data->state_name }}, {{ $data->country_name }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                 <div id= "pendingtransact_{{$listdata[0]->supplier_id }}" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>List Pending Transaction</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable3" class="table table-bordered table-vcenter">
                                    <thead>
                                        <tr>
                                            
                                            <th class="text-center">Module</th>
                                            <th class="text-center">Total Pending Transaction</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach ($listPendingTransaction as  $indexKey => $data)   
                                        <tr>
                                            
                                            
                                            <td class="text-center">{{ $data['transaction'] }}</td>
                                            <td class="text-center">{{ $data['total'] }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id= "bank_{{$listdata[0]->supplier_id }}" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>List Bank: @if(count($listSupplierBank) > 0){{ count($listSupplierBank) }}@endif</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable4" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Bank Name</th>
                                            <th class="text-center">Acc. No.</th>
                                            <th class="text-center">Acc. Purpose</th>
                                            <th class="text-center">Is Default</th>
                                            <th class="text-center">Is HQ</th>
                                            <th class="text-center">Bank Branch</th>
                                            <th class="text-center">Changed Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach ($listSupplierBank as  $indexKey => $data)   
                                        <tr>
                                            <td class="text-center">{{ ++$indexKey }}</td>
                                            <td class="text-center">{{ $data->fin_org_name }}  <span class="@if($data->bank_status == 9) text-danger @endif">({{ App\Services\EPService::$RECORD_STATUS[$data->bank_status] }})</span></td>
                                            <td class="text-center">{{ $data->account_no }}</td>
                                            <td class="text-center">{{ $data->account_purpose }}</td>
                                            <td class="text-center">{{ $data->is_default_account }}</td>
                                            <td class="text-center">{{ $data->is_for_hq }}</td>
                                            <td class="text-center">{{ $data->bank_branch }}</td>
                                            <td class="text-center">{{ $data->changed_date }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="block">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>Jumlah Pengguna Pembekal : {{ count($listdata) }}</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            
                            <div class="table-responsive">
                                <table id="" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Name</th>
                                            <th class="text-center">Identification No.</th>
                                            <th class="text-center">Email</th>
                                            <th class="text-center">Role</th>
                                            <th class="text-center">Softcert Status</th>
                                            <th class="text-center"></th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                    @foreach ($listdata as  $indexKey => $data)   
                                        <tr>
                                            <td class="text-center">{{ ++$indexKey }}</td>
                                            <td class="text-center">{{ $data->p_name }}</td>
                                            <td class="text-center">
                                            @if(Auth::user()->isAdvRolesEp())    
                                                <a href="{{url('/find/userpersonnel/')}}/{{ $data->appl_id }}/{{ $data->personnel_id }}" >{{ $data->p_identification_no }}</a>
                                            @else
                                                {{ $data->p_identification_no }}
                                            @endif
                                            </td>
                                            <td class="text-center">{{ $data->p_email }}</td>
                                            <td class="text-center">{{ $data->p_ep_role }}</td>
                                            <td class="text-center">{{ $data->p_is_softcert }}</td>
                                            <td class="text-center">
                                                <button class="btn btn-info btn-xs"
                                                   data-toggle="collapse" data-target="#row_{{$data->personnel_id}}" >
                                                    Details</button></td>
                                        </tr>
                                        <tr id="row_{{$data->personnel_id}}"  @if(strlen($data->p_ep_role) > 0)class="collapsed" @else class="collapse" @endif>
                                            <td class="text-center" colspan="7">
                                                <!-- Customer Addresses Block -->
                                                <div class="block" @if($data->p_ep_role == 'MOF_SUPPLIER_ADMIN')style="background-color: inherit;"@endif>
                                                    <div class="row">
                                                        @if ($data->is_activate_key == true)
                                                        <div class="col-sm-12"  class="text-left">
                                                            <div class="notify-alert alert alert-success alert-dismissable">
                                                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                                                <h4><i class="fa fa-exclamation-triangle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4> 
                                                                Sila semak <a href="javascript:void(0)" class="alert-link">e-mel {{$data->p_email}} di INBOX / SPAM</a> untuk aktifkan login ID.
                                                                <br /> <br />
                                                                    <strong>Link Activation </strong> :  <a target="_blank" href="{{  $data->link }}">{{  $data->link }}</a> <br />
                                                                    <strong>Activation Key </strong> :  {{  $data->activation_key }}<br />
                                                                    <strong>Status Success Send </strong> :  {{  $data->is_sent }} <br />
                                                                    <strong>Last Date Changed </strong> :  {{  $data->activation_changed_date }} <br />

                                                            </div>

                                                        </div>
                                                        @endif

                                                        <div class="col-sm-4">
                                                            <div class="block">
                                                                <div class="block-title">
                                                                    <h2>Maklumat User Login  </h2>
                                                                </div>
                                                                <address class="text-left">
                                                                    <strong>User ID </strong> : {{ $data->user_id  }}<br />
                                                                    <strong>Login ID </strong> : {{ $data->login_id  }}<br />
                                                                    <strong>Email </strong> : {{ $data->email  }}<br />
                                                                    <strong>ICNO </strong> : {{ $data->identification_no  }}<br />
                                                                    <strong>Mobile </strong> : {{ $data->mobile_country }}{{ $data->mobile_area }}{{ $data->mobile_no }}<br />
                                                                    <strong>Record Status </strong> : {{ $data->u_record_status }}<br />
                                                                    <strong>Last Login Date </strong> : {{ $data->login_date }}<br />
                                                                    <strong>Last Date Changed </strong> :  {{  $data->changed_date }} <br />

                                                                    <br />
                                                                    <strong>Peranan</strong> : <br/>
                                                                    @if($data->roles && count($data->roles) > 0 )
                                                                        @foreach ($data->roles as $role)
                                                                            {{ $role->role_code }} <br/>
                                                                        @endforeach
                                                                    @else
                                                                    Tiada
                                                                    @endif
                                                                </address>
                                                            </div>
                                                        </div>

                                                         <div class="col-sm-4">
                                                            <div class="block">
                                                                <div class="block-title">
                                                                    <h2>Maklumat Diri  Staf Syarikat </h2>
                                                                </div>
                                                                <address  class="text-left">
                                                                    <strong>Personal ID </strong> : {{ $data->personnel_id  }}<br />
                                                                    <strong>Designation </strong> : {{ $data->p_designation  }}<br />
                                                                    <strong>Email </strong> : {{ $data->p_email  }}<br />
                                                                    <strong>Role </strong> : {{ $data->p_ep_role }}<br />
                                                                    <strong>Mobile </strong> : {{ $data->p_mobile_country }}{{ $data->p_mobile_area }}{{ $data->p_mobile_no }}<br />
                                                                    <strong>SoftCert Status </strong> : {{ $data->p_is_softcert  }}<br />
                                                                    <strong>Is Equity Owner? </strong> : {{ $data->is_equity_owner  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Is Contract Signer? </strong> : {{ $data->is_contract_signer  }}<br />
                                                                    <strong>Is Authorized? </strong> : {{ $data->is_authorized  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Is Contact Person? </strong> : {{ $data->is_contact_person  }}<br />
                                                                    <strong>Is Management? </strong> : {{ $data->is_mgt  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Is Director? </strong> : {{ $data->is_director  }}<br />
                                                                    <strong>Is Bumi? </strong> : {{ $data->is_bumi  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Record Status </strong> : {{ $data->p_record_status }}<br />
                                                                    <strong>Last Date Changed </strong> :  {{  $data->p_changed_date }} <br />
                                                                    <strong>Rev. No. </strong> :  {{  $data->p_rev_no }} <br />
                                                                </address>
                                                            </div>
                                                         </div>

                                                        @if(count($data->listSoftCert ) > 0)
                                                        <div class="col-sm-4">
                                                            <div class="block softcert">
                                                                <div class="block-title">
                                                                    <h2>SoftCert Request</h2>
                                                                </div>
                                                                
                                                                
                                                                <a href='#modal-osbdetail-spki'
                                                                    class='pull-left modal-list-data-action'
                                                                    data-toggle='modal'
                                                                    data-url='/find/success-signing/{{$data->identification_no}}/SPKI'
                                                                    data-title='Last Successful Signing' 
                                                                    style="color:darkblue;font-weight: bolder;text-decoration: underline;" >
                                                                    <i class="fa fa-send"></i>  
                                                                    Last Successful Signing
                                                                </a>
                                                                <br /><br />
                                                                    
                                                                @foreach ($data->listSoftCert as $sCert)
                                                                <address  class="text-left ">
                                                                    <strong>SoftCert Request ID </strong> : {{ $sCert->softcert_request_id  }}<br /> 
                                                                    @if($sCert->is_trustgate == true)
                                                                    <a target='_blank'
                                                                       style="color:darkblue;font-weight: bolder;text-decoration: underline;"
                                                                       href="https://digitalid.msctrustgate.com/nextgenEP/ePRA/migrate/detail_migrate?epNo={{$sCert->ep_no}}&icNo={{$data->p_identification_no}}" >
                                                                        Check TrustGate (@if($sCert->is_trustgate_data == true)Expiry on  {{$sCert->trustgate_expired_date}}@endif)</a><br />
                                                                    @endif
                                                                    <strong>Record Status </strong> : {{ $sCert->record_status }}<br />
                                                                    <strong>Date Created </strong> :  {{  $sCert->created_date }} <br />
                                                                    <strong>Last Date Changed </strong> :  {{  $sCert->changed_date }} <br />
                                                                    <strong>Using Free SoftCert</strong> :  {{  $sCert->is_free }} <br />
                                                                    {{--
                                                                    <strong>Response Status</strong> :  {{  $sCert->response_status }} <br />
                                                                    <strong>Request Mode</strong> :  {{  $sCert->request_mode }} <br />
                                                                    <strong>Remark </strong> : {{ $sCert->remark  }}<br />
                                                                    <strong>Reason Code </strong> : {{ $sCert->reason_code  }}<br />
                                                                    --}}
                                                                    @if(strlen($sCert->cert_serial_no)>0)
                                                                    <strong>Cert Serial NO. </strong> : {{ $sCert->cert_serial_no  }}<br />
                                                                    <strong>Cert Issuer </strong> : @if($sCert->cert_issuer=='T') TrustGate @else Digicert @endif<br />
                                                                    <strong>Cert Valid From </strong> : {{ $sCert->valid_from  }}<br />
                                                                    <strong>Cert Valid To</strong> : {{ $sCert->valid_to  }}<br />
                                                                    <strong>Cert Record Status </strong> : {{ $sCert->pdc_record_status }}<br />
                                                                    <strong>Cert Last Date Changed </strong> :  {{  $sCert->pdc_changed_date }} <br />
                                                                    @else

                                                                        @if($sCert->is_success_SPK020 > 0)
                                                                            <a href='#modal-osbdetail-spki'
                                                                               class='modal-list-data-action'
                                                                               data-toggle='modal'
                                                                               data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-020/'
                                                                               data-title='Sent Request to SPKI Detail'>
                                                                                <strong style="color:forestgreen;">Successful sent request to SPKI</strong><br />
                                                                            </a>
                                                                        @endif
                                                                        @if($sCert->is_success_SPK010 > 0)
                                                                            <a href='#modal-osbdetail-spki'
                                                                               class='modal-list-data-action'
                                                                               data-toggle='modal'
                                                                               data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-010/'
                                                                               data-title='Received Update from SPKI Detail'>
                                                                                <strong style="color:forestgreen;">Successfully received update from SPKI</strong><br />
                                                                            </a>
                                                                        <span style="font-style: italic">{{ $sCert->remark }}</span><br />
                                                                        @endif

                                                                    <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />

                                                                    @endif
                                                                </address>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                        @endif

                                                    </div>
                                                </div> 
                                            </td>
                                        </tr>
                                    @endforeach    
                                    </tbody>
                                </table>
                            </div>
                            
                            
                            @foreach ($listdata as $data)
                            
                            
                            
                            
                               
                            @endforeach   
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>

        <!-- MODAL: KOD BIDANG -->
        <div id="modal-category-code" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i> List Category Code</h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="table-responsive">
                                    <table id="datatable-category-code" class="table table-striped table-vcenter">
                                        <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-left">Category</th>
                                            <th class="text-center">Category Type</th>
                                            <th class="text-left">Officer Decision</th>
                                            <th class="text-center" style="display: none;">Remarks</th>
                                            <th class="text-center">Status</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach ($listSupplierCategoryCode as  $indexKey => $data)
                                            <tr>
                                                <td class="text-center">{{ ++$indexKey }}</td>
                                                <td class="text-left">
                                                    {{ $data->category_l1_code }} - {{ $data->category_l1_name }}<br/>
                                                    {{ $data->category_l2_code }} - {{ $data->category_l2_name }}<br/>
                                                    {{ $data->category_l3_code }} - {{ $data->category_l3_name }}<br/>
                                                </td>
                                                <td class="text-center">{{ $data->is_special_category === 1 ? "Special" : "Normal" }}</td>
                                                <td class="text-left">
                                                    <strong>Approved Date :</strong> {{ $data->approved_date }}<br/>
                                                    <strong>PO :</strong> {{ $data->is_approved_by_po }}<br/>
                                                    <strong>Approver :</strong> {{ $data->is_approved_by_ap }}
                                                </td>
                                                <td class="text-left" style="display: none;">
                                                    <strong>PO :</strong> {{ $data->previous_po_remark }}<br/>
                                                    <strong>Approver :</strong> {{ $data->previous_ap_remark }}
                                                </td>
                                                <td class="text-center">{{ $data->record_status }}</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>

        <!-- MODAL: SPKI SOFTCERT -->
        <div id="modal-osbdetail-spki" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Title</span></h2>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="osb-detail"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif


@endsection


@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function(){ TablesDatatables.init(); });</script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        $(document).ready(function () {

            $('.softcert').on("click", '.modal-list-data-action', function () {

                $('.spinner-loading').show();
                $('.osb-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data)
                        $('.spinner-loading').hide();
                        $('.osb-detail').html($data).fadeIn();
                    }
                });

            });
        });
    </script>
@endsection

