{"name": "cdccrm-integration-upgrade", "version": "1.0.0", "description": "<p align=\"center\"><a href=\"https://laravel.com\" target=\"_blank\"><img src=\"https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg\" width=\"400\" alt=\"Laravel Logo\"></a></p>", "main": "index.js", "directories": {"test": "tests"}, "dependencies": {"ansi-regex": "^5.0.1", "ansi-styles": "^4.3.0", "asynckit": "^0.4.0", "axios": "^1.8.4", "call-bind-apply-helpers": "^1.0.2", "chalk": "^4.1.2", "cliui": "^8.0.1", "color-convert": "^2.0.1", "color-name": "^1.1.4", "combined-stream": "^1.0.8", "concurrently": "^9.1.2", "delayed-stream": "^1.0.0", "detect-libc": "^2.0.3", "dunder-proto": "^1.0.1", "emoji-regex": "^8.0.0", "enhanced-resolve": "^5.18.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "esbuild": "^0.25.2", "escalade": "^3.2.0", "fdir": "^6.4.3", "follow-redirects": "^1.15.9", "form-data": "^4.0.2", "function-bind": "^1.1.2", "get-caller-file": "^2.0.5", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "graceful-fs": "^4.2.11", "has-flag": "^4.0.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "is-fullwidth-code-point": "^3.0.0", "jiti": "^2.4.2", "laravel-vite-plugin": "^1.2.0", "lightningcss": "^1.29.2", "lightningcss-win32-x64-msvc": "^1.29.2", "lodash": "^4.17.21", "math-intrinsics": "^1.1.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "nanoid": "^3.3.11", "picocolors": "^1.1.1", "picomatch": "^4.0.2", "proxy-from-env": "^1.1.0", "require-directory": "^2.1.1", "rollup": "^4.40.0", "rxjs": "^7.8.2", "shell-quote": "^1.8.2", "source-map-js": "^1.2.1", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "supports-color": "^8.1.1", "tapable": "^2.2.1", "tinyglobby": "^0.2.12", "tree-kill": "^1.2.2", "tslib": "^2.8.1", "vite": "^6.3.1", "vite-plugin-full-reload": "^1.2.0", "wrap-ansi": "^7.0.0", "y18n": "^5.0.8", "yargs": "^17.7.2", "yargs-parser": "^21.1.1"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.10"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "tailwind-init": "tailwindcss init -p", "tailwind-build": "node node_modules/tailwindcss/dist/lib.js -i ./resources/css/app.css -o ./public/css/tailwind.css"}, "keywords": [], "author": "", "license": "ISC"}