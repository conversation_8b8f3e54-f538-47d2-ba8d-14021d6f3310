<?php

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\PendingTransactionService;
use Carbon\Carbon;
use SSH;

class EpController extends Controller {

    use SupplierService;
    use OSBService;
    use ProfileService;
    use PendingTransactionService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function getIdentity($icNo){
        $icObj = array();
        if($icNo == '111111002222'){
            return view('identity', [
                'type' => 'identity',
                'carian' => null,
                'result' => null
            ]);
        }
        
        $icno = trim(str_replace("-", "", $icNo));
        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:crs='http://tempuri.org/CRSService'><soapenv:Header/><soapenv:Body><crs:retrieveCitizensDataReq><AgencyCode>130101</AgencyCode><BranchCode>JPNCRS</BranchCode><UserId>JPNADM2</UserId><TransactionCode>T2</TransactionCode><RequestDateTime>2016-04-26T10:41:00</RequestDateTime><ICNumber>$icno</ICNumber><RequestIndicator>A</RequestIndicator></crs:retrieveCitizensDataReq></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $urlIdentity = "https://esb.myidentity.gov.my:81/crsservice";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        SSH::into('osb')->run($commands, function($line) use ($icno,&$icObj)  {
            $data = $line.PHP_EOL;
            if (strpos($data, $icno) !== false) {
                $p = xml_parser_create();
                xml_parse_into_struct($p, $data, $vals, $index);
                xml_parser_free($p);
                
                //string(16) "SOAPENV:ENVELOPE" string(12) "SOAPENV:BODY" string(27) "NS1:RETRIEVECITIZENSDATARES" string(10) "AGENCYCODE" string(10) "BRANCHCODE" string(6) "USERID" string(15) "TRANSACTIONCODE" string(13) "REPLYDATETIME" string(8) "ICNUMBER" string(14) "REPLYINDICATOR" string(11) "MESSAGECODE" string(4) "NAME" string(11) "DATEOFBIRTH" string(6) "GENDER" string(4) "RACE" string(8) "RELIGION" string(17) "PERMANENTADDRESS1" string(17) "PERMANENTADDRESS2" string(17) "PERMANENTADDRESS3" string(24) "PERMANENTADDRESSPOSTCODE" string(24) "PERMANENTADDRESSCITYCODE" string(25) "PERMANENTADDRESSSTATECODE" string(22) "CORRESPONDENCEADDRESS1" string(22) "CORRESPONDENCEADDRESS2" string(22) "CORRESPONDENCEADDRESS3" string(22) "CORRESPONDENCEADDRESS4" string(22) "CORRESPONDENCEADDRESS5" string(29) "CORRESPONDENCEADDRESSPOSTCODE" string(29) "CORRESPONDENCEADDRESSCITYCODE" string(36) "CORRESPONDENCEADDRESSCITYDESCRIPTION" string(30) "CORRESPONDENCEADDRESSSTATECODE" string(32) "CORRESPONDENCEADDRESSCOUNTRYCODE" string(13) "ADDRESSSTATUS" string(12) "RECORDSTATUS" string(12) "VERIFYSTATUS" string(31) "CORRESPONDENCEADDRESSUPDATEDATE" string(29) "CORRESPONDENCEADDRESSUPDATEBY" string(11) "DATEOFDEATH" string(11) "OLDICNUMBER" string(17) "RESIDENTIALSTATUS" string(17) "CITIZENSHIPSTATUS" string(12) "EMAILADDRESS" string(17) "MOBILEPHONENUMBER" string(11) "NEWICNUMBER" string(24) "PERMANENTADDRESSCITYDESC" string(27) "NS1:RETRIEVECITIZENSDATARES" string(12) "SOAPENV:BODY" string(16) "SOAPENV:ENVELOPE"
                foreach($vals as $val){
                    if($val["tag"] == 'ICNUMBER') $icObj["icno"] = $val["value"];
                    if($val["tag"] == 'NAME') $icObj["name"] = $val["value"];
                    if($val["tag"] == 'GENDER') $icObj["gender"] = EPService::$JPN_GENDER[$val["value"]];
                    if($val["tag"] == 'PERMANENTADDRESS1'){
                        if(array_key_exists('value', $val))$icObj["addr1"] = $val["value"];else$icObj["addr1"] ='';
                    } 
                    if($val["tag"] == 'PERMANENTADDRESS2'){
                        if(array_key_exists('value', $val))$icObj["addr2"] = $val["value"];else$icObj["addr2"] ='';
                    }
                    if($val["tag"] == 'PERMANENTADDRESS3') {
                        if(array_key_exists('value', $val))$icObj["addr3"] = $val["value"];else$icObj["addr3"] ='';
                    }
                    if($val["tag"] == 'PERMANENTADDRESSPOSTCODE') $icObj["postcode"] = $val["value"];
                    if($val["tag"] == 'PERMANENTADDRESSCITYDESC') $icObj["city"] = $val["value"];
                    if($val["tag"] == 'PERMANENTADDRESSSTATECODE') $icObj["statecode"] = $val["value"] . " - " . EPService::$JPN_STATE[$val["value"]];
                
                    if($val["tag"] == 'EMAILADDRESS') {
                        if(array_key_exists('value', $val))$icObj["email"] = $val["value"];else$icObj["email"] ='';
                    }
                    if($val["tag"] == 'MOBILEPHONENUMBER') {
                        if(array_key_exists('value', $val))$icObj["mobile"] = $val["value"];else$icObj["mobile"] ='';
                    }
                }
                //dd($icObj );
            }

        });

        return view('identity', [
            'type' => 'identity',
            'carian' => $icNo,
            'result' => $icObj
        ]);
    }
    
    

    public function detailsUserByEpNo() {
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'epno',
            'result' => null,
            'carian' => '']);
    }
    public function getDetailsUserByEpNo($epno){
        $epNo = trim($epno);
        $result = $this->searchFilterSupplier($epNo, "EPNO");
        if($result["status"] == true){
            return redirect($result["redirect"]);
        }
        $checkEpNo = substr($epno, 0, 3);
        if( $checkEpNo != 'eP-'){
            // This carian come from Company Name o SSM No.
            $resultEpNo = $this->getEpNoSmSupplier($epno);
            if($resultEpNo != null){
                $epNo  = $resultEpNo;
            }else{
                $resultEpNo2 = $this->getEpNoSmSupplierByApplNo($epno);
                if($resultEpNo2 != null){
                    $epNo  = $resultEpNo2;
                }
            }
        }
        $list = $this->getSMSupplierUsersDetailsByMofNOorEpNo(null, $epNo);
        return $this->getDetailsUsersSupplier($list,$epNo,'epno');
    }
    public function detailsUserByMof() {
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'mofno',
            'result' => null,
            'carian' => '']);
    }
    public function getDetailsUserByMof($mofno) {
        
        $mofNo = trim($mofno);
        $result = $this->searchFilterSupplier($mofNo, "MOFNO");
        if($result["status"] == true){
            return redirect($result["redirect"]);
        }
        
        $list = $this->getSMSupplierUsersDetailsByMofNOorEpNo($mofNo, null);
        return $this->getDetailsUsersSupplier($list,$mofNo,'mofno');
    }
    
    public function detailsUserBySapVendorCode() {
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'sap',
            'result' => null,
            'carian' => '']);
    }
    public function getDetailsUserBySapVendorCode($sapVendorCode) {
        $epNo = $this->getEpNoBySapVendorCode($sapVendorCode);
        if($epNo && strlen($epNo) > 0 ){
            $list = $this->getSMSupplierUsersDetailsByMofNOorEpNo(null, $epNo);
            return $this->getDetailsUsersSupplier($list,$sapVendorCode,'sap');
        }
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'sap',
            'result' => null,
            'carian' => '']);
    }

    public function listUom() {
        return view('list_uom', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'uom',
            'result' => null,
            'carian' => '']);
    }

    public function getListUom($uom) {
        $uomTemp = strtolower($uom);
        $list = $this->getListUomByUomName($uomTemp);
        if(count($list) > 0){
            return view('list_uom',[
                    'listdata'=> $list , 'carian' => $uom]);
        }
        //return "MAAF! CARIAN TIDAK DIJUMPAI.";
        return view('list_uom', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'uom',
            'result' => 'notfound',
            'carian' => $uom]);
    }

    public function getDetailsUsersSupplier($list,$carian,$type){
        $isNotAppointedAdmin = true;
        if(count($list) > 0){
            
            /** Repopulate Data to bind others data **/
            foreach ($list as $data) {
                
                //populate put to $data
                $this->populatePersonnelUserData($data);
                
                if(strlen($data->p_ep_role) > 0){
                    $isNotAppointedAdmin = false;
                }

            }
            $listWorkFlow = $this->getWorkFlowSupplierProcess($list[0]->supplier_id, $list[0]->latest_appl_id);
            $listAttachmentCancelReject = $this->getListAttachmentRejectOrCancel($list[0]->latest_appl_id);
            $listRemarksCancelReject = $this->getListRemarksRejectOrCancel($list[0]->latest_appl_id);
            $totalItems = $this->getTotalItemsSupplier($list[0]->supplier_id);
            $sapVendorCode = $this->getMainSapVendorCode($list[0]->ep_no);
            $suppMofStatus = $this->getSupplierMofStatus($list[0]->supplier_id);
            $listSuppMofVirtCert = $this->getSupplierMofVirtCert($list[0]->supplier_id);
            $listSuppPayment = $this->getPaymentSuppliers($list[0]->supplier_id);
            $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcess($list[0]->supplier_id);
            $basicCompInfo = $this->getBasicSupplierInfo($data->latest_appl_id);
            if($basicCompInfo && $basicCompInfo != null){
                $basicCompInfo->company_basic_id = $basicCompInfo->company_basic_id;
                $basicCompInfo->is_with_federal= EPService::$YES_NO[$basicCompInfo->is_with_federal];
                $basicCompInfo->is_with_state = EPService::$YES_NO[$basicCompInfo->is_with_state];
                $basicCompInfo->is_with_statutory = EPService::$YES_NO[$basicCompInfo->is_with_statutory];
                $basicCompInfo->is_with_glc = EPService::$YES_NO[$basicCompInfo->is_with_glc];
                $basicCompInfo->is_with_others = EPService::$YES_NO[$basicCompInfo->is_with_others];
            }

            /** Check MOF expiry **/
            $list[0]->is_mof_expired = false;
            if($list[0]->ma_exp_date) {
                $mofExpDate = Carbon::parse($list[0]->ma_exp_date);
                $now = Carbon::now();

                $list[0]->is_mof_expired = $mofExpDate < $now;
            }
            
            $listSupplierBranch= $this->getListSupplierBranch($list[0]->latest_appl_id);
            if(count($listSupplierBranch) > 0){
                foreach ($listSupplierBranch as $branch) {
                    $branch->sap_vendor_code = '-';
                    $branchSapVendorCode = $this->getMainSapVendorCodeByBranchCode($list[0]->ep_no, $branch->branch_code);
                    if($branchSapVendorCode) {
                        $branch->sap_vendor_code = $branchSapVendorCode->sap_vendor_code;
                    }
                }
            }
            $listSupplierBank= $this->getListSupplierBank($list[0]->latest_appl_id);
            $hqGstInfo = $this->getHqGstInfo($data->supplier_id);
            $listSupplierCategoryCode = $this->getListSupplierCategoryCode($data->latest_appl_id);
            if(count($listSupplierCategoryCode) > 0){
                foreach ($listSupplierCategoryCode as $cat) {
                    $cat->is_approved_by_po = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_po];
                    $cat->is_approved_by_ap = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_ap];
                    $cat->record_status = EPService::$RECORD_STATUS[$cat->record_status];
                }
            }
            
            $listPendingTransaction = $this->getListPendingTransaction($list[0]->supplier_id);
            
            $isPendingTransaction = false;
            
             /** Condition to show flag danger **/
            foreach ($listPendingTransaction as $data) {
                
                //  dump($data);
                if($data->get('total') > 0){
                    $isPendingTransaction = true;
                    break;
                }

            }

            return view('user_details_mof',[
                'listdata'=> $list,
                'isNotAppointedAdmin' => $isNotAppointedAdmin ,
                'totalItems' => $totalItems,
                'sapVendorCode' => $sapVendorCode,
                'basicCompInfo' => $basicCompInfo,
                'suppMofStatus' => $suppMofStatus,
                'listSuppPayment' => $listSuppPayment,
                'listSuppMofVirtCert' => $listSuppMofVirtCert,
                'listSupplierBranch' => $listSupplierBranch,
                'listSupplierBank' => $listSupplierBank,
                'listSupplierCategoryCode' => $listSupplierCategoryCode,
                'listPendingTransaction' => $listPendingTransaction,
                'isPendingTransaction' =>$isPendingTransaction,
                'hqGstInfo' => $hqGstInfo,
                'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
                'listWorkFlow' => $listWorkFlow ,
                'listAttachmentCancelReject' => $listAttachmentCancelReject,
                'listRemarksCancelReject' => $listRemarksCancelReject,
                'carian' => $carian,'type' => $type]);
        }
        //return "MAAF! CARIAN TIDAK DIJUMPAI.";
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'mofno',
            'result' => 'notfound',
            'carian' => $carian]);
    }

    
    public function getLatestSuccessSigning($icno,$type) {
        $data = $this->getLastSigningSPKIorGPKI($icno,$type);

        return view('include.signing_success', [
            'dataSign' => $data
        ]);
    }
    
    public function getSoftcertLogDetail($softReqID, $serviceCode) {
        $xml = $this->getDetailCertSPKI($softReqID, $serviceCode);

        return view('include.osb_logging_detail', [
            'xml' => $xml
        ]);
    }

    public function detailsUser() {
        return view('user_details', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'icno',
            'result' => null,
            'carian' => '']);
    }
    
    public function getDetailsUser($icNo) {
        $icno = trim($icNo);
        
        $result = $this->searchFilterSupplier($icno, "ICNO");
        if($result["status"] == true){
            return redirect($result["redirect"]);
        }
        
        $list =  $this->getSMSupplierUsersActiveByICNO($icno);

        if(count($list) > 0){
            foreach ($list as $data) {
                //populate put to $data
                $this->populatePersonnelUserData($data);
                

                $data->is_business_network = false;

                $listWorkFlow = $this->getWorkFlowSupplierProcess($data->supplier_id, $data->latest_appl_id);
                $totalItems = $this->getTotalItemsSupplier($data->supplier_id);
                $sapVendorCode = $this->getMainSapVendorCode($data->ep_no);
                $suppMofStatus = $this->getSupplierMofStatus($data->supplier_id);
                $listSuppMofVirtCert = $this->getSupplierMofVirtCert($data->supplier_id);
                $listSuppPayment = $this->getPaymentSuppliers($data->supplier_id);
                $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcess($data->supplier_id);
                $basicCompInfo = $this->getBasicSupplierInfo($data->latest_appl_id);
                if($basicCompInfo && $basicCompInfo != null){
                    $data->is_business_network = true;
                    $data->company_basic_id = $basicCompInfo->company_basic_id;
                    $data->is_with_federal= EPService::$YES_NO[$basicCompInfo->is_with_federal];
                    $data->is_with_state = EPService::$YES_NO[$basicCompInfo->is_with_state];
                    $data->is_with_statutory = EPService::$YES_NO[$basicCompInfo->is_with_statutory];
                    $data->is_with_glc = EPService::$YES_NO[$basicCompInfo->is_with_glc];
                    $data->is_with_others = EPService::$YES_NO[$basicCompInfo->is_with_others];
                }
                
                $listSupplierBranch= $this->getListSupplierBranch($data->latest_appl_id);
                $listSupplierBank= $this->getListSupplierBank($data->latest_appl_id);
                $hqGstInfo = $this->getHqGstInfo($data->supplier_id);
                

            }
            return view('user_details',[
                'listdata'=> $list, 
                'totalItems' => $totalItems,
                'sapVendorCode' => $sapVendorCode,
                'suppMofStatus' => $suppMofStatus,
                'listSuppPayment' => $listSuppPayment,
                'listSuppMofVirtCert' => $listSuppMofVirtCert,
                'listSupplierBranch' => $listSupplierBranch,
                'listSupplierBank' => $listSupplierBank,
                'hqGstInfo' => $hqGstInfo,
                'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
                'listWorkFlow' => $listWorkFlow ,
                'carian' => $icno]
            );
        }
        return view('user_details', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'icno',
            'result' => 'notfound',
            'carian' => $icno]);
    }
    
    protected function searchSupplier($search){
        $checkMof = substr($search, 0, 4);
        $checkEpNo = substr($search, 0, 2);
        if($checkMof == '357-' || $checkMof == '465-'){
            return redirect('find/mofno/'.$search);
        }
        else if($checkEpNo == 'eP-'){
            return redirect('find/epno/'.$search);
        }
        else {
            return redirect('find/icno/'.$search);
        }
    }
    
    
    /**
     * Switch Carian - Easy Seacrh on UI. Auto Switch
     * @param type $search
     * @param type $type
     * @return boolean
     */
    protected function searchFilterSupplier($search,$type){
        $result = array('status' => false); //to set no auto switch. 
        
        $checkMof = substr($search, 0, 4);
        $checkEpNo = substr($search, 0, 3);
        $checkIcno = strlen(trim($search));
            
        if($type == 'ICNO'){
            if($checkMof == '357-' || $checkMof == '465-'){
               $result = array(
                    'status' => true,
                    'redirect' =>'find/mofno/'.$search
                ); 
            }else if( $checkEpNo == 'eP-'){
                $result = array(
                    'status' => true,
                    'redirect' =>'find/epno/'.$search
                );
            }else if($checkIcno == 12){
                $result = array('status' => false); 
            }
            else{
               $result = array(
                    'status' => true,
                    'redirect' =>'find/epno/'.$search
                );
            }
        }
        if($type == 'MOFNO'){
            if($checkMof == '357-' || $checkMof == '465-'){
                $result = array('status' => false);  
            }
            else if( $checkEpNo == 'eP-'){
                $result = array(
                    'status' => true,
                    'redirect' =>'find/epno/'.$search
                );
            }else if($checkIcno == 12){
                $result = array(
                    'status' => true,
                    'redirect' =>'find/icno/'.$search
                );
            }else{
                $result = array(
                    'status' => true,
                    'redirect' =>'find/epno/'.$search
                );
            }
        }
        if($type == 'EPNO'){
            if($checkMof == '357-' || $checkMof == '465-'){
                $result = array(
                    'status' => true,
                    'redirect' =>'find/mofno/'.$search
                ); 
            }
            else if( $checkEpNo == 'eP-'){
                $result = array('status' => false); 
            }else if($checkIcno == 12){
                $result = array(
                    'status' => true,
                    'redirect' =>'find/icno/'.$search
                );
            }
            else{
                $result = array('status' => false); 
            }
        }
        
        return $result;
    }
   
}
