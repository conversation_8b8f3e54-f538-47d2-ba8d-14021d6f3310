<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/1gfmas/ws')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian WebService Log 1GFMAS<br>
            <small><PERSON><PERSON> berk<PERSON><PERSON> PurchaseRequest, ContractRequest, ContractOrderNo, PurchaseOrderNo, 
                ItemCode </small>
        </h1>
    </div>
    
    <!-- Log Block -->
        <div class="block">
            <!-- Log Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Log</strong> 1GFMAS Info</h2>
            </div>
            <!-- END Log Title -->

            <!-- Log Content -->
            <div class="table-responsive">
                <table class="table table-bordered table-vcenter">
                    <tbody>
                        <tr>
                            <td>GFM-020</td>
                            <td class="text-center">MMINF</td>
                            <td class="text-success"><strong>MasterDataMaterial</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-080</td>
                            <td class="text-center">MM503</td>
                            <td class="text-info"><strong>DebitAdviceNote</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-100</td>
                            <td class="text-center">MM501</td>
                            <td class="text-success"><strong>POContractForGoodsAndServices</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-110</td>
                            <td class="text-center">MM504</td>
                            <td class="text-danger"><strong>FulfillmentReceivingNote</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-120</td>
                            <td class="text-center">MM506</td>
                            <td><strong>PaymentInstruction</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-130  ||  EPP-013</td>
                            <td class="text-center">MM507</td>
                            <td><strong>ChargingCheckingStatus</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-070</td>
                            <td class="text-center">MM513</td>
                            <td><strong>StopInstruction</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- END Log Content -->
        </div>
        <!-- END Log Block -->

</div>

<?php if($listdata == null || count($listdata) == 0): ?>
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              <p>Tidak dijumpai!</p>
          </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if($listdata && count($listdata) > 0): ?>
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
        </div>
        
        <?php if($listdata && count($listdata) > 0 ): ?>
        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $xml): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="block collapse panel-xml" id="<?php echo e($xml->trans_type); ?>_<?php echo e($xml->service_code); ?>_<?php echo e($xml->trans_id); ?>">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> <?php echo e($xml->trans_type); ?> | (<?php echo e($xml->service_code); ?>) | <?php echo e($xml->trans_id); ?></h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup"><?php echo e($xml->payload_body); ?></code>
            </pre>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    
        <ul class="text-info">
            <li>IBReq : Refering to request</li>
            <li>IBRes : Refering to response  </li>
            <li>Click link in column service code to view each payload for transaction type.  </li>
        </ul>
        <div class="table-responsive">
            <table id="ws-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">TRANS ID</th>
                        <th class="text-center">TRANS TYPE</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">TRANS DATE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">STATUS CODE</th>
                        <th class="text-center">STATUS DESC</th>
                        <th class="text-center">REMARKS 1</th>
                        <th class="text-center">REMARKS 2</th>
                        <th class="text-center">REMARKS 3</th>
                    </tr>
                </thead>
                <tbody>
                <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="text-center"><?php echo e($data->trans_id); ?></td>
                        <td class="text-center"><?php echo e($data->trans_type); ?></td>
                        <td class="text-center">
                        <a href="javascript:void(0)" data-toggle="collapse" data-target="#<?php echo e($data->trans_type); ?>_<?php echo e($data->service_code); ?>_<?php echo e($data->trans_id); ?>" >
                            <?php echo e($data->service_code); ?></a></td>
                        <td class="text-center"><?php echo e($data->trans_date); ?></td>
                        <td class="text-left"><?php echo e(App\Services\EPService::$OSB_STATUS[$data->status]); ?></td>
                        <td class="text-left"><?php echo e($data->status_code); ?></td>
                        <td class="text-left">
                            <?php echo $__env->make('_shared._infoDetailErrorDescription', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </td>
                        <td class="text-center"><?php echo e($data->remarks_1); ?></td>
                        <td class="text-center"><?php echo e($data->remarks_2); ?></td>
                        <td class="text-center"><?php echo e($data->remarks_3); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->

</div>
<?php endif; ?>

<?php echo $__env->make('_shared._modalDetailInfo', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_1gfmas.blade.php ENDPATH**/ ?>