<?php

namespace App\Http\Controllers;

use App\Services\Traits\BPMService;


class BPMController extends Controller {

    use BPMService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function getListTaskBpm(){
        return view('list_task_bpm', [
            'listdata' => null,
            'carian' => '']);
    }
    public function getListTaskBpmByDocNo($docNo){
        $list = $this->getTaskBpmByDocNo($docNo);
        return view('list_task_bpm', [
            'listdata' => $list,
            'carian' => $docNo]);
    }

}
