<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;


class PaymentSupplierController extends Controller {
    use SupplierService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function listPendingPaymentSupplier(){
        
        $listData = $this->getListPendingPaymentSuppliers();
        
        //$list = $listData->whereIn('order_id',$listOrderIdFailed)->all();
        return view('list_pending_payment', [
            'listdata' => $listData,
            'listXml' => null,
            'carian' => '']);
    }
    public function searchPendingPaymentSupplier($search){
        $list = $this->getListPendingPaymentSuppliers();
        return view('list_pending_payment', [
            'listdata' => $list,
            'carian' => $search]);
    }
    
    public function removePendingPaymentSupplier($orderID){
        $data = $this->getPendingPaymentSuppliersByOrderID($orderID); 
        if($data && $data->order_id != null){
            $check = DB::connection('mysql_ep_support')->table('ep_payment_failed')
                    ->where('order_id',$orderID)->count();
            if($check == 0){
                DB::connection('mysql_ep_support')
                    ->insert('insert into ep_payment_failed 
                        (   supplier_id,company_name,ep_no,
                            order_id,payment_amt,bill_no,bill_type,bill_date,
                            bill_ref_id,payment_due_date,payment_created,
                            payment_gateway,created_at,created_by) 
                        values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', 
                        [   
                            $data->supplier_id,
                            $data->company_name,
                            $data->ep_no,
                            $data->order_id,
                            $data->payment_amt,
                            $data->bill_no,
                            $data->bill_type,
                            Carbon::parse($data->bill_date),
                            $data->bill_ref_id,
                            Carbon::parse($data->payment_due_date),
                            Carbon::parse($data->created_date),
                            $data->payment_gateway,
                            Carbon::now(),
                            Auth::user()->user_name  ]);
                return array('status'=>'00','desc'=>'success inserted');
            }else{
                return array('status'=>'99','desc'=>'record existed');
            } 
        }
        return array('status'=>'99','desc'=>'failed inserted');
    }

    function isHighest($array){
        if(is_array($array)){
            foreach($array as $key => $value){
                $array[$key] = $this->isHighest($value);
            }
            return max($array);

        }else{
            return $array;
        }
    }

    function isHighestMonth($arr, $val){
        foreach($arr as $item){
            if ($item[1] == $val)
                return $item[0];
        }
        return "-";
    }

    public function showPaymentStat(){
        $paymentPercentage = $this->getPaymentStatPercentage();
        $paymentStatInfo = $this->getPaymentStatInfo();

        $dataMonth = array();
        $dataMonthNum = array();
        $dataYear = array();
        $dataSumAmount = array();
        $count = 1;
        $sumAll = 0;
        $currentEarn = 0;
        
        $highestEarnMth = '';
        $highestEarnAmount = '';
        foreach ($paymentPercentage as $data) {
            array_push($dataMonth, [$count, $data->month]);
            array_push($dataMonthNum, [$count, $data->month_num]);
            array_push($dataYear, [$count, $data->year]);
            array_push($dataSumAmount, [$count, $data->sum_amount]);

            $sumAll += $data->sum_amount;
            $currentEarn = $data->sum_amount;
            
            if($currentEarn > $highestEarnAmount){
                $highestEarnAmount = $currentEarn;
                $highestEarnMth = $data->month;
            }
            
            $count++;
        }
        $highestEarnAmt = number_format($highestEarnAmount,2);
        //$highestEarnAmt = number_format($this->isHighest($dataSumAmount),2);
        //$highestEarnMth = $dataMonth[$this->isHighestMonth($dataSumAmount, max($dataSumAmount[1]))][1];

        $paymentPercentage[0]->summ_all = number_format($sumAll,2);
        $paymentPercentage[0]->current_earn = number_format($currentEarn,2);
        $paymentPercentage[0]->highest_earn_amount = $highestEarnAmt;
        $paymentPercentage[0]->highest_earn_month = $highestEarnMth;

        $countInfo = 0;
        $statProcessFeeAmt = array();
        $statRegisterFeeAmt = array();
        $statSoftcertFeeAmt = array();

        $currentMonth = date('m');
        $currentMonthAmt = 0;
        $statSumAll = 0;
        $statProcessTotal = 0;
        $statRegisterTotal = 0;
        $statSoftcertTotal = 0;

        foreach ($paymentStatInfo as $pInfo) {
            if($pInfo->bill_type === 'Processing Fee') {
                $statProcessTotal += $pInfo->total_amt;
                array_push($statProcessFeeAmt, [$pInfo->month, $pInfo->total_amt]);
            } elseif($pInfo->bill_type === 'Registration Fee') {
                $statRegisterTotal += $pInfo->total_amt;
                array_push($statRegisterFeeAmt, [$pInfo->month, $pInfo->total_amt]);
            } elseif($pInfo->bill_type === 'Softcert Fee') {
                $statSoftcertTotal += $pInfo->total_amt;
                array_push($statSoftcertFeeAmt, [$pInfo->month, $pInfo->total_amt]);
            }

            $statSumAll += $pInfo->total_amt;

            if($pInfo->month == $currentMonth){
                $currentMonthAmt += $pInfo->total_amt;
            }

            $countInfo++;
        }

        $paymentStatInfo[0]->current_month_total = $currentMonthAmt;
        $paymentStatInfo[0]->sum_process = $statProcessTotal;
        $paymentStatInfo[0]->sum_register = $statRegisterTotal;
        $paymentStatInfo[0]->sum_softcert = $statSoftcertTotal;
        $paymentStatInfo[0]->sum_all = $statSumAll;

        return view('payment_stat', [
            'dataList' => $paymentPercentage,
            'dataStats' => $paymentStatInfo,
            'dataMonth' => $dataMonth,
            //'dataMonthNum' => $dataMonthNum,
            //'dataYear' => $dataYear,
            'dataSumAmount' => $dataSumAmount,
            'statProcessFeeAmt' => $statProcessFeeAmt,
            'statRegisterFeeAmt' => $statRegisterFeeAmt,
            'statSoftcertFeeAmt' => $statSoftcertFeeAmt
        ]);
    }

}
