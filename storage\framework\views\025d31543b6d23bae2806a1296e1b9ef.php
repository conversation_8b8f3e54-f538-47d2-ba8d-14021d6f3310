<?php $__env->startSection('header'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <div class="block">
        <div class="block-title">
            <h2><strong>APIVE</strong> Trigger</h2>
            <div class="block-options pull-right action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="<?php echo e(url('/support/report/log/task-apive')); ?>/<?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?>" 
                   data-title="List Action Trigger APIVE Today ">View Today Action</a>
            </div>
        </div>

        <form id="form-search-mminf" action="<?php echo e(url("/trigger/gfmas/apive/search")); ?>" method="post" class="form-horizontal" onsubmit="return true;">
            <?php echo e(csrf_field()); ?>

            <input name="_method" id="_method" type="hidden" value="POST">
            <div class="form-group">
                <label class="col-md-3 control-label" for="ep_no">eP No. <span class="text-danger">*</span></label>
                <div class="col-md-5">
                    <input id="ep_no" name="ep_no" class="form-control" placeholder="eP No.." type="text" required
                           <?php if(isset($formSearch)): ?> value="<?php echo e($formSearch["ep_no"]); ?>" <?php endif; ?>>
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
        </form>
    </div>
    <?php if($result && $result != 'notfound'): ?>
        <div class="block">
            <div class="table-responsive">
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                    <tr>
                        <th class="text-center">eP No.</th>
                        <th class="text-center">Supplier ID</th>
                        <th class="text-center">Company Name</th>
                        <th class="text-center">MOF No.</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center"></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php $__currentLoopData = $result; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="text-center"><a href="<?php echo e(url("/find/gfmas/apive")); ?>/<?php echo e($data->ep_no); ?>" target="_blank" data-toggle="tooltip" title="Check APIVE"><?php echo e($data->ep_no); ?></a></td>
                            <td class="text-center"><?php echo e($data->supplier_id); ?></td>
                            <td class="text-center"><a href="<?php echo e(url("/find/epno")); ?>/<?php echo e($data->ep_no); ?>" target="_blank" data-toggle="tooltip" title="Company Information"><?php echo e($data->company_name); ?> (<?php echo e($data->reg_no); ?>) </a></td>
                            <td class="text-center"><?php echo e($data->mof_no); ?></td>
                            <td class="text-center"><?php echo e($data->record_status); ?></td>
                            <td class="text-center"><?php echo e($data->created_date); ?></td>
                            <td class="text-center td-<?php echo e($data->supplier_id); ?>"><?php echo e($data->changed_date); ?></td>
                            <td class="text-center action_table">
                                <input name="supplier_id" id="supplier_id"  type="hidden" value="<?php echo e($data->supplier_id); ?>">
                                <input name="current_changed_date" id="current_changed_date"  type="hidden" value="<?php echo e($data->changed_date); ?>">
                                <input name="ep_no" id="ep_no"  type="hidden" <?php if(isset($formSearch, $formSearch["ep_no"])): ?> value="<?php echo e($formSearch["ep_no"]); ?>" <?php endif; ?>>
                                <div class="btn-group btn-group-xs">
                                    <a class="btn btn-primary action_trigger"  title="" data-original-title="Trigger!"
                                       href="#modal_confirm_trigger" data-toggle="modal" data-id="<?php echo e($data->ep_no); ?>"><i class="hi hi-transfer"></i> Trigger</a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
    <?php if($result == 'notfound'): ?>
        <div class="block block-alt-noborder full text-center label-primary">
              <span style="color: #FFF;">Tidak dijumpai!</span>
        </div>
    <?php endif; ?>

    <div id="modal_confirm_trigger" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"> Are you sure want to update this supplier to be triggered? </h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                                <?php echo e(csrf_field()); ?>

                                <div class="form-group">
                                    <label class="col-md-3 control-label">eP No. </label>
                                    <div class="col-md-9">
                                        <input type="hidden" id="trigger_epno" value="" />
                                        <p id="trigger_epno_display" class="form-control-static"></p>
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="button" class="btn btn-sm btn-primary action_confirm_trigger"><i class="gi gi-ok_2"></i> Yes</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    <?php echo $__env->make('_shared._modalListLogAction', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
    <script>

        //Confirm Trigger Dialogue
        $('td.action_table').on("click",'a.action_trigger', function(){
            var ePno = $(this).attr('data-id');
            var changedDate = $("#current_changed_date").val();
            console.log("eP No.: "+ePno);
            console.log("changedDate: "+changedDate);
            $("#trigger_epno").val(ePno);
            $("#trigger_epno_display").text(ePno);
        });
        $('div.form-actions').on("click",'button.action_confirm_trigger', function(){
            var ePno = $("#trigger_epno").val();
            var changedDate = $("#current_changed_date").val();
            var supplierId = $("#supplier_id").val();
            var csrf = $("input[name=_token]").val();
            console.log("eP No.: "+ePno);
            console.log("changedDate: "+changedDate);

            $('#modal_confirm_trigger').modal('hide');
            $('#wait-modal').modal('toggle');

            $.ajax({
                url: "/trigger/gfmas/apive/update",
                method : "POST",
                dataType : "json",
                data : {"_token":csrf,"ep_no":ePno,"changed_date":changedDate},
                context: document.body
            }).done(function(resp) {
                if(resp.status === 'success'){
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Successfully Triggered!");
                    $("td.td-"+supplierId).addClass("text-success");
                    $("td.td-"+supplierId).html(resp.value);
                    $('#wait-modal').modal('hide');
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Trigger Failed! Please try again.");
                    $("td.td-"+supplierId).addClass("text-danger");
                    $('#wait-modal').modal('hide');
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\apive_trigger.blade.php ENDPATH**/ ?>