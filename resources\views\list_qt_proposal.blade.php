@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/qt/proposal/')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian <PERSON> Sebut Harga <br>
                <small>Semakan serahan pembekal untuk sebut harga.</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian Semakan Tawaran Sebut Harga </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">QT NO.</th>
                            <th class="text-center">PROPOSAL NO</th>
                            <th class="text-center">MOF NO.</th>
                            <th class="text-center">SUPPLIER</th>
                            <th class="text-center">SUBMITTED</th>
                            <th class="text-center">PROPOSAL SUBMITTION DATE</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->qt_no }}</td>
                                <td class="text-center">{{ $data->proposal_no }}</td>
                                <td class="text-center content-link">
                                    <a class="modal-list-data-action"
                                       href="{{url('/find/mofno')}}/{{$data->mof_no }}" 
                                       target='_blank' >
                                       {{ $data->mof_no }} </a>
                                <td class="text-left">{{ $data->supplier_name }}</td>
                                <td class="text-left">{{ $data->submitted }}</td>
                                <td class="text-left">{{ $data->proposal_submit_date }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



