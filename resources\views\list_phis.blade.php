@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/phis/ws')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian WebService Log PHIS<br>
            <small>Carian berkenaan ContractRequestNo, FulfillmentReceivedNote, ContractOrderNo, ProductCode, 
                ItemDescription, StopInstructionNo, EPInvoiceNo, DeliveryOrderNo, PTJCode, PhysicalContactNo </small>
        </h1>
    </div>
    
    <!-- Log Block -->
        <div class="block">
            <!-- Log Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Log</strong> PHIS Info</h2>
            </div>
            <!-- END Log Title -->

            <!-- Log Content -->
            <div class="table-responsive">
                <table class="table table-bordered table-vcenter">
                    <tbody>
                        <tr>
                            <td>PHS-080</td>
                            <td class="text-success"><strong>PHISMasterDataDeliveryAddress</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-150</td>
                            <td class="text-success"><strong>ContractInformation</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-160</td>
                            <td class="text-info"><strong>ContractOrderInformation</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-170</td>
                            <td class="text-info"><strong>ContractFulfilmentReceivedNote</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-180</td>
                            <td class="text-success"><strong>ContractStopInstruction</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-190</td>
                            <td class="text-danger"><strong>ContractPaymentInstruction</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-200</td>
                            <td><strong>ContractPaymentEffectNotification</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-210</td>
                            <td><strong>ContractRequestCreation</strong></td>
                            <td>PHIS sent to eP</td>
                        </tr>
                        <tr>
                            <td>PHS-220</td>
                            <td><strong>PHISMasterDataMaterialInformation</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- END Log Content -->
        </div>
        <!-- END Log Block -->

</div>

@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              <p>Tidak dijumpai!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        
        @if($listdata && count($listdata) > 0 )
        @foreach($listdata as $xml)
        <div class="block collapse panel-xml" id="{{$xml->trans_type}}_{{$xml->trans_id}}">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> {{$xml->trans_type}} | ({{$xml->service_code}}) | {{$xml->trans_id}}</h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup">{{$xml->payload_body}}</code>
            </pre>
        </div>
        @endforeach
        @endif
    
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">TRANS ID</th>
                        <th class="text-center">TRANS TYPE</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">TRANS DATE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">REMARKS 1</th>
                        <th class="text-center">REMARKS 2</th>
                        <th class="text-center">REMARKS 3</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center">{{$data->trans_id }}</td>
                        <td class="text-center">{{ $data->trans_type }}</td>
                        <td class="text-center">
                        <a href="javascript:void(0)" data-toggle="collapse" data-target="#{{ $data->trans_type }}_{{$data->trans_id}}" >
                            {{$data->service_code }}</a></td>
                        <td class="text-center">{{ $data->trans_date }}</td>
                        <td class="text-left">{{ $data->status_desc }}</td>
                        <td class="text-center">{{ $data->remarks_1 }}</td>
                        <td class="text-center">{{ $data->remarks_2 }}</td>
                        <td class="text-center">{{ $data->remarks_3 }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endif

@endsection
