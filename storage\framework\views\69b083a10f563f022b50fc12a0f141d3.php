<?php $__env->startSection('header'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

        <div class="block full">
            <!-- Inline Form Title -->
            <div class="block-title">
                <h2><strong>1GFMAS Out</strong> Folder</h2>
            </div>
            <!-- END Inline Form Title -->

            <div class="row">
                <!-- Inline Form Content -->
                <form action="page_forms_general.php" method="post" class="form-inline" onsubmit="return false;">
                    <div class="form-group col-md-3" style="display: none;">
                        <label class="control-label" for="folder_fetch">Folder to Fetch</label>
                        <select id="folder_fetch" name="folder_fetch" class="form-control" size="1">
                            <option value="APOVE">eP OUT</option>
                            <option value="AP511" selected>1GFMAS OUT</option>
                            <option value="APERR">eP IN</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label class="control-label" for="process_id">Process</label>
                        <select id="process_id" name="process_id" class="form-control" size="1">
                            <option value="APOVE">APOVE</option>
                            <option value="AP511" selected>AP511</option>
                            <option value="APERR">APERR</option>
                            <option value="AR902">AR902</option>
                            <option value="CMBNK">CMBNK</option>
                            <option value="GLBAC">GLBAC</option>
                            <option value="GLDNA">GLDNA</option>
                            <option value="GLPRG">GLPRG</option>
                            <option value="GLPRO">GLPRO</option>
                            <option value="GLSEG">GLSEG</option>
                            <option value="GLVOT">GLVOT</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3 form-actions pull-right">
                        <button id="fetch_btn" type="submit" class="btn btn-sm btn-primary action_fetch enable-tooltip" data-url='/list/1gfmas/folder' data-original-title="Trigger Batch Process">
                            <i class="fa fa-arrow-down"></i> Fetch
                        </button>
                        <button class="btn btn-sm btn-default action_refresh" data-url='/list/1gfmas/fetch'>
                            <i class="fa fa-refresh"></i> Refresh
                        </button>
                    </div>
                </form>
            </div>

            <!-- END Inline Form Content -->
        </div>

        <div class="block remove-padding">
            <div class="table-responsive">
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <div id="wait" class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                <table id="basic-datatable" class="table table-vcenter table-striped table-bordered display-none"></table>
            </div>
        </div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
    <script>

        var APP_URL = <?php echo json_encode(url('/')); ?>


        $(document).ready(function () {



            function destroyDataTable() {
                var tableListData = $('#basic-datatable').DataTable();

                return tableListData.destroy();
            }


            function initDataTable() {
                /* Initialize Datatables */
                $('#basic-datatable').DataTable({
                    columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                    pageLength: 10,
                    lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                });
            }

            function refreshDataTable() {
                destroyDataTable();
                $('#wait').show();
                $('#basic-datatable').hide();

                $.ajax({
                    url: APP_URL + '/list/1gfmas/fetch',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#wait').hide();
                        $('#basic-datatable').html($data).fadeIn();

                        initDataTable();
                    }
                });
            }

            $.ajax({
                url: APP_URL + '/list/1gfmas/fetch',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#wait').hide();
                    $('#basic-datatable').hide().html($data).fadeIn();

                    App.datatables();
                    initDataTable();

                }
            });

            //REFRESH BUTTON
            $('div.form-actions').on("click", 'button.action_refresh', function () {
                refreshDataTable();
            });


            //FETCH BUTTON
            $('div.form-actions').on("click", 'button.action_fetch', function () {
                var folder = $("#folder_fetch").val();
                var processId = $("#process_id").val();
                var csrf = $("input[name=_token]").val();

                destroyDataTable();
                $('#wait').show();
                $('#basic-datatable').hide();

                $.ajax({
                    url: "/fetch/1gfmas/1gfmas-out",
                    method: "POST",
                    dataType: "json",
                    data: {
                        "_token": csrf,
                        "process_id": processId,
                        "folder": folder
                    },
                    context: document.body,
                    error: function(jqXHR, textStatus, errorThrown)
                    {
                        alert(errorThrown);
                    },
                }).done(function (resp) {
                    if (resp.state == 'true') {
                        alert(resp.msg);
                        refreshDataTable();
                    } else {
                        alert(resp.msg);
                        refreshDataTable();
                    }
                });
            });

            //ON PROCESS CHANGE
            $('#process_id').change(function() {
                $('#fetch_btn').attr('data-original-title', 'Trigger ' + $('#process_id').val() + ' Batch Process');
            });

        });



    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\fetch_1gfmas.blade.php ENDPATH**/ ?>