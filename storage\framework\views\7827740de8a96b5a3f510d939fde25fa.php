<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find')); ?>/<?php echo e($type); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <?php if($listdata == null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <?php if($result == 'notfound'): ?>
                        <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                        <small>Rekod tidak dijumpai!</small>
                    <?php else: ?>
                        <i class="gi gi-search"></i>Carian <?php if($type == 'mofno'): ?>MOF No. <?php else: ?> eP No. <?php endif; ?><br>
                        <small>Masukkan <?php if($type == 'mofno'): ?>MOF No. <?php else: ?> eP No. <?php endif; ?> pada carian diatas...</small>
                    <?php endif; ?>
                </h1>
            </div>
        </div>
    <?php endif; ?>

    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title  panel-heading" style = "background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>ePerolehan (Pembekal) : <?php echo e($listdata[0]->company_name); ?></strong></h1>
                </div>
                <div class="row">
                    <?php if($isNotAppointedAdmin == true): ?>
                    <div class="col-sm-12">
                        <div class="notify-alert alert alert-warning alert-dismissable">
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                            <h4><i class="fa fa-exclamation-triangle"></i> Admin Pembekal masih belum dilantik!</h4> 
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="col-sm-3">
                        <div class="block">
                            
                            <div class="block-title">
                                <h2>Maklumat Syarikat  </h2>
                            </div>
 
                            <h6><strong><?php echo e($listdata[0]->company_name); ?></strong></h6>
                            <address>
                                <strong>Supplier ID </strong> : <?php echo e($listdata[0]->supplier_id); ?><br />
                                <strong>Appl ID </strong> : <?php echo e($listdata[0]->latest_appl_id); ?><br />
                                <strong>Business Type </strong> : <?php echo e($listdata[0]->business_type); ?>  &raquo;  (<?php echo e(App\Services\EPService::$BUSINESS_TYPE[$listdata[0]->business_type]); ?>)<br />
                                <strong>SSM No </strong> : <?php echo e($listdata[0]->reg_no); ?><br />
                                <strong>eP No </strong> : <?php echo e($listdata[0]->ep_no); ?>  <a target="_blank" href="<?php echo e(url('/find/gfmas/apive/')); ?>/<?php echo e($listdata[0]->ep_no); ?>" >Check Apive</a><br />
                                <strong>Establish Date </strong> : <?php echo e($listdata[0]->establish_date); ?> <br />
                                <strong>Last Date Changed </strong> : <?php echo e($listdata[0]->s_changed_date); ?> <br />
                                
                                <strong>Record Status </strong> : <span class="bolder"><?php echo e($listdata[0]->s_record_status); ?> </span><br />
                                
                                <?php if($basicCompInfo): ?>
                                <strong>Address </strong> : ( Address ID: <?php echo e($basicCompInfo->address_id); ?> )<br />
                                <?php echo e($basicCompInfo->address_1); ?><br />
                                <?php echo e($basicCompInfo->address_2); ?> <?php echo e($basicCompInfo->address_3); ?><br />
                                <?php echo e($basicCompInfo->postcode); ?> <?php echo e($basicCompInfo->city_name); ?>, <?php echo e($basicCompInfo->district_name); ?><br />
                                <?php echo e($basicCompInfo->state_name); ?>, <?php echo e($basicCompInfo->country_name); ?><br />
                                <?php endif; ?>
                                <strong>Phone No: </strong> : <?php echo e($basicCompInfo->phone_country); ?><?php echo e($basicCompInfo->phone_area); ?><?php echo e($basicCompInfo->phone_no); ?> <br /> 
                                <strong>Fax No: </strong> : <?php echo e($basicCompInfo->fax_country); ?><?php echo e($basicCompInfo->fax_area); ?><?php echo e($basicCompInfo->fax_no); ?> <br /> <br /> 
                                
                                <?php if($hqGstInfo): ?>
                                    <strong>GST Registration No. : </strong> <?php echo e($hqGstInfo->gst_reg_no); ?><br />
                                    <strong>GST Effective Date : </strong> <?php echo e($hqGstInfo->gst_eff_date); ?><br /><br />
                                <?php else: ?>
                                    <strong>GST Registration No. : </strong> Not Registered <br /><br />
                                <?php endif; ?>

                                <strong>Total Items Catalogs (Approved & Published) </strong> : <a href="<?php echo e(url('find/items/supplier')); ?>/<?php echo e($listdata[0]->ep_no); ?>" 
                                                                                                   class="btn btn-info btn-xs"  target="_blank" title="View Detail All Items" ><?php echo e($totalItems); ?></a><br />
                                
                                <br />
                                <strong>SAP Vendor Code</strong> : <?php if($sapVendorCode): ?><?php echo e($sapVendorCode->sap_vendor_code); ?> <?php else: ?> Tiada <?php endif; ?><br />
                                
                                <br />
                                <strong>In Progress Application History</strong> : <?php if(count($listinProgressSuppProcessAppl) > 0): ?>
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#app_progress_<?php echo e($listdata[0]->supplier_id); ?>" ><?php echo e($listinProgressSuppProcessAppl[0]->appl_no); ?></span> <?php else: ?> Tiada <?php endif; ?><br />
                                  
                                    
                                
                                <br />
                                <strong>Payment History</strong> : <?php if($listSuppPayment): ?>
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#payment_<?php echo e($listdata[0]->supplier_id); ?>" ><?php echo e(count($listSuppPayment)); ?></span> <?php else: ?> Tiada <?php endif; ?><br />
                                
                               
                            </address>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="block">

                            <div class="block-title">
                                <h2>Maklumat MOF</h2>
                            </div>
 
                            <address>
                                <strong>MOF NO </strong> : <?php echo e($listdata[0]->mof_no); ?><br />
                                <strong>Effective Date </strong> : <?php echo e($listdata[0]->ma_eff_date); ?><br />
                                <strong>Expired Date </strong> : <span class=" <?php if($listdata[0]->is_mof_expired): ?> label label-danger <?php endif; ?>"><?php echo e($listdata[0]->ma_exp_date); ?></span><br />
                                <strong>Record Status </strong> : <?php echo e($listdata[0]->ma_record_status); ?><br />
                                
                                <?php if($suppMofStatus): ?>
                                <br />
                                <strong>Bumi Status </strong> : <?php echo e($suppMofStatus->bumi_status); ?><br />
                                <strong>Registration Type </strong> : <?php echo e($suppMofStatus->type); ?><br />
                                <?php endif; ?>
                             
                                
                                <br />
                                <strong>Cert Mof</strong> : <br/>
                                <?php if($listSuppMofVirtCert && count($listSuppMofVirtCert) > 0): ?>
                                    <?php $__currentLoopData = $listSuppMofVirtCert; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cerVirt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php echo e($cerVirt->cert_serial_no); ?> (<?php echo e($cerVirt->cert_type); ?>)  #<?php echo e($cerVirt->appl_id); ?>

                                        <a href="<?php echo e(url('/download/mofcert')); ?>/<?php echo e($cerVirt->cert_serial_no); ?>" target="_blank" >Download</a> 
                                        <?php if($cerVirt->appl_id != $listdata[0]->latest_appl_id): ?>
                                        <span class="text-danger">(Data Fix!) Appl ID tidak sama</span><?php endif; ?>
                                        <br/>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                Tiada
                                <?php endif; ?>

                                <br />
                                <strong>List branch</strong> : <?php if(count($listSupplierBranch) > 0 ): ?>
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#branch_<?php echo e($listdata[0]->supplier_id); ?>" ><?php echo e(count($listSupplierBranch)); ?></span> <?php else: ?> Tiada <?php endif; ?><br />
                                
                                <br />
                                <strong>List Bank</strong> : <?php if($listSupplierBank): ?>
                                    <span  class="btn btn-info btn-xs" data-toggle="collapse" data-target="#bank_<?php echo e($listdata[0]->supplier_id); ?>" ><?php echo e(count($listSupplierBank)); ?></span>
                                    <?php $__currentLoopData = $listSupplierBank; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bank): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>  <?php if($bank->bank_status == 9): ?> <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;"></i> <?php break; ?> <?php endif; ?> <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?> Tiada <?php endif; ?><br />

                                <br />
                                <strong>List Category Code</strong> : <?php if(count($listSupplierCategoryCode) > 0 ): ?>
                                    <a href="#modal-category-code" data-toggle="modal" class="btn btn-info btn-xs"><?php echo e(count($listSupplierCategoryCode)); ?></a> <?php else: ?> Tiada <?php endif; ?><br />
                                <br />
                                <strong>List Pending Transaction</strong> : <?php if($isPendingTransaction == true): ?>
                                    <span  class="btn btn-danger btn-xs " data-toggle="collapse" data-target="#pendingtransact_<?php echo e($listdata[0]->supplier_id); ?>" <i class="gi gi-circle_exclamation_mark " style="font-size: 10pt;"></i> Details</span><?php else: ?> Tiada <?php endif; ?><br />
                                 <br />


                            </address>
                        </div>
                    </div>
                    <?php if($basicCompInfo): ?>
                    <div class="col-sm-3">
                        <div class="block">

                            <div class="block-title">
                                <h2>Business Network</h2>
                            </div>
 
                            <address>
                                <strong>Federal? </strong> : <?php echo e($basicCompInfo->is_with_federal); ?><br />
                                <strong>State? </strong> : <?php echo e($basicCompInfo->is_with_state); ?><br />
                                <strong>Local Council? </strong> : <?php echo e($basicCompInfo->is_with_statutory); ?><br />
                                <strong>GLC? </strong> : <?php echo e($basicCompInfo->is_with_glc); ?><br />
                                <strong>Others? </strong> : <?php echo e($basicCompInfo->is_with_others); ?><br />
                            </address>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if(count($listWorkFlow) > 0): ?>
                    <div class="col-sm-3">
                        <div class="block">

                            <div class="block-title">
                                <h2>Application Status</h2>
                            </div>
                            <?php if(count($listWorkFlow)>0): ?>
                            <strong>Appl ID</strong> : <?php echo e($listWorkFlow[0]->appl_id); ?><br />
                            <strong>Appl No</strong> : <?php echo e($listWorkFlow[0]->appl_no); ?><br />
                            <strong>Appl Record  Status</strong> : <?php echo e(App\Services\EPService::$RECORD_STATUS[$listWorkFlow[0]->record_status]); ?> &nbsp;
                            <?php if($listWorkFlow[0]->record_status == 9): ?> <i class="gi gi-circle_exclamation_mark text-danger" title="Ask Technical to review this issue!" style="font-size: 10pt;"></i><?php endif; ?><br /><br />
                            <?php endif; ?>
                            <?php $__currentLoopData = $listWorkFlow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $datawf): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <address>
                                <strong>Is Current</strong> : <?php echo e($datawf->is_current); ?> &nbsp; , &nbsp; 
                                <strong>Status </strong> : <?php echo e($datawf->wf_status); ?><br />
                                <strong>Created Date</strong> : <?php echo e($datawf->wf_created_date); ?><br />
                            </address>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                            
                            <?php if(isset($listRemarksCancelReject) && count($listRemarksCancelReject) > 0): ?>
                            <strong><span class="bolder">Remarks </span></strong><br />
                            <?php $__currentLoopData = $listRemarksCancelReject; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objRemark): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <strong><?php echo e($objRemark->doc_type); ?></strong> : <span style="font-style: italic"><?php echo e($objRemark->remark); ?></span>  <br/>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <br/><br/>
                            <?php endif; ?>
                            
                            <?php if(isset($listAttachmentCancelReject) && count($listAttachmentCancelReject) > 0): ?>
                            <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                            <?php $__currentLoopData = $listAttachmentCancelReject; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objAttReject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <strong><a href="<?php echo e(url('/download/attachment/cancel-reject/')); ?>/<?php echo e($objAttReject->attachment_id); ?>" target="_blank" ><?php echo e($objAttReject->doc_type); ?> - <?php echo e($objAttReject->file_name); ?></a> </strong>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <br/><br/>
                            <?php endif; ?>
                            
                            
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div id= "app_progress_<?php echo e($listdata[0]->supplier_id); ?>" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>In Progress Application History: (<?php if(count($listinProgressSuppProcessAppl) > 0): ?><?php echo e($listinProgressSuppProcessAppl[0]->appl_no); ?><?php endif; ?>)</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable1" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Appl Type</th>
                                            <th class="text-center">Appl No.</th>
                                            <th class="text-center">Is Active Appl</th>
                                            <th class="text-center">Appl Created By</th>
                                            <th class="text-center">Appl Changed Date</th>
                                            <th class="text-center">Is Resubmit</th>
                                            <th class="text-center">WF Created Date</th>
                                            <th class="text-center">WF Changed Date</th>
                                            <th class="text-center">WF Status</th>
                                            <th class="text-center">WF Is Current</th>
                                            
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $listinProgressSuppProcessAppl; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                        <tr>
                                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                            <td class="text-center"><?php echo e($data->appl_type); ?></td>
                                            <td class="text-center"><?php echo e($data->appl_no); ?></td>
                                            <td class="text-center"><?php echo e($data->is_active_appl); ?></td>
                                            <td class="text-center"><?php echo e($data->appl_created_by); ?></td>
                                            <td class="text-center"><?php echo e($data->changed_date); ?></td>
                                            <td class="text-center"><?php echo e($data->is_resubmit); ?></td>
                                            <td class="text-center"><?php echo e($data->wf_created_date); ?></td>
                                            <td class="text-center"><?php echo e($data->wf_changed_date); ?></td>
                                            <td class="text-center"><?php echo e($data->wf_status); ?></td>
                                            <td class="text-center"><?php echo e($data->is_current); ?></td>
                                            
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                
                <div id= "payment_<?php echo e($listdata[0]->supplier_id); ?>" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>Payment History : <?php echo e(count($listSuppPayment)); ?></strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable2" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Bill No.</th>
                                            <th class="text-center">Bill Type</th>
                                            <th class="text-center">Bill Date</th>
                                            <th class="text-center">Bill Amount</th>
                                            <th class="text-center">Order ID</th>
                                            <th class="text-center">Payment Date</th>
                                            <th class="text-center">Payment Mode</th>
                                            <th class="text-center">Status</th>
                                            <th class="text-center">Receipt No.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $listSuppPayment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                        <tr>
                                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                            <td class="text-center"><?php echo e($data->bill_no); ?></td>
                                            <td class="text-center"><?php echo e($data->bill_type); ?></td>
                                            <td class="text-center"><?php echo e($data->bill_date); ?></td>
                                            <td class="text-center"><?php echo e($data->payment_amt); ?></td>
                                            <td class="text-center"><?php echo e($data->payment_id); ?> - <?php echo e($data->payment_gateway); ?></td>
                                            <td class="text-center"><?php echo e($data->payment_date); ?></td>
                                            <td class="text-center"><?php echo e($data->payment_mode); ?></td>
                                            <td class="text-center"><?php echo e($data->status); ?></td>
                                            <td class="text-center"><?php echo e($data->receipt_no); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id= "branch_<?php echo e($listdata[0]->supplier_id); ?>" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>List Branch: <?php if(count($listSupplierBranch) > 0): ?><?php echo e(count($listSupplierBranch)); ?><?php endif; ?></strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable3" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Branch Name</th>
                                            <th class="text-center">Branch Code</th>
                                            <th class="text-center">SAP Vendor Code</th>
                                            <th class="text-center">Changed Date</th>
                                            <th class="text-left">GST</th>
                                            <th class="text-center">Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $listSupplierBranch; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                        <tr>
                                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                            <td class="text-center"><?php echo e($data->branch_name); ?></td>
                                            <td class="text-center"><?php echo e($data->branch_code); ?></td>
                                            <td class="text-center"><?php echo e($data->sap_vendor_code); ?></td>
                                            <td class="text-center"><?php echo e($data->changed_date); ?></td>
                                            <td class="text-left">
                                                <strong>Registration No. :</strong> <?php echo e($data->gst_reg_no); ?><br />
                                                <strong>Effective Date :</strong> <?php echo e($data->gst_eff_date); ?><br />
                                            </td>
                                            <td class="text-center">
                                                <?php echo e($data->address_1); ?>,
                                                <?php echo e($data->address_2); ?>,
                                                <?php echo e($data->address_3); ?><br />
                                                <?php echo e($data->postcode); ?> <?php echo e($data->city_name); ?>, <?php echo e($data->district_name); ?><br />
                                                <?php echo e($data->state_name); ?>, <?php echo e($data->country_name); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                 <div id= "pendingtransact_<?php echo e($listdata[0]->supplier_id); ?>" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>List Pending Transaction</strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable3" class="table table-bordered table-vcenter">
                                    <thead>
                                        <tr>
                                            
                                            <th class="text-center">Module</th>
                                            <th class="text-center">Total Pending Transaction</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $listPendingTransaction; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                        <tr>
                                            
                                            
                                            <td class="text-center"><?php echo e($data['transaction']); ?></td>
                                            <td class="text-center"><?php echo e($data['total']); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id= "bank_<?php echo e($listdata[0]->supplier_id); ?>" class="block collapse">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>List Bank: <?php if(count($listSupplierBank) > 0): ?><?php echo e(count($listSupplierBank)); ?><?php endif; ?></strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table id="basic-datatable4" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Bank Name</th>
                                            <th class="text-center">Acc. No.</th>
                                            <th class="text-center">Acc. Purpose</th>
                                            <th class="text-center">Is Default</th>
                                            <th class="text-center">Is HQ</th>
                                            <th class="text-center">Bank Branch</th>
                                            <th class="text-center">Changed Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $listSupplierBank; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                        <tr>
                                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                            <td class="text-center"><?php echo e($data->fin_org_name); ?>  <span class="<?php if($data->bank_status == 9): ?> text-danger <?php endif; ?>">(<?php echo e(App\Services\EPService::$RECORD_STATUS[$data->bank_status]); ?>)</span></td>
                                            <td class="text-center"><?php echo e($data->account_no); ?></td>
                                            <td class="text-center"><?php echo e($data->account_purpose); ?></td>
                                            <td class="text-center"><?php echo e($data->is_default_account); ?></td>
                                            <td class="text-center"><?php echo e($data->is_for_hq); ?></td>
                                            <td class="text-center"><?php echo e($data->bank_branch); ?></td>
                                            <td class="text-center"><?php echo e($data->changed_date); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="block">
                    <div class="block-title " style = "background-color: #ffc074;">
                        <h2><i class="fa fa-users"></i> <strong>Jumlah Pengguna Pembekal : <?php echo e(count($listdata)); ?></strong></h2>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            
                            <div class="table-responsive">
                                <table id="" class="table table-striped table-vcenter">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-center">Name</th>
                                            <th class="text-center">Identification No.</th>
                                            <th class="text-center">Email</th>
                                            <th class="text-center">Role</th>
                                            <th class="text-center">Softcert Status</th>
                                            <th class="text-center"></th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                    <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                        <tr>
                                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                            <td class="text-center"><?php echo e($data->p_name); ?></td>
                                            <td class="text-center">
                                            <?php if(Auth::user()->isAdvRolesEp()): ?>    
                                                <a href="<?php echo e(url('/find/userpersonnel/')); ?>/<?php echo e($data->appl_id); ?>/<?php echo e($data->personnel_id); ?>" ><?php echo e($data->p_identification_no); ?></a>
                                            <?php else: ?>
                                                <?php echo e($data->p_identification_no); ?>

                                            <?php endif; ?>
                                            </td>
                                            <td class="text-center"><?php echo e($data->p_email); ?></td>
                                            <td class="text-center"><?php echo e($data->p_ep_role); ?></td>
                                            <td class="text-center"><?php echo e($data->p_is_softcert); ?></td>
                                            <td class="text-center">
                                                <button class="btn btn-info btn-xs"
                                                   data-toggle="collapse" data-target="#row_<?php echo e($data->personnel_id); ?>" >
                                                    Details</button></td>
                                        </tr>
                                        <tr id="row_<?php echo e($data->personnel_id); ?>"  <?php if(strlen($data->p_ep_role) > 0): ?>class="collapsed" <?php else: ?> class="collapse" <?php endif; ?>>
                                            <td class="text-center" colspan="7">
                                                <!-- Customer Addresses Block -->
                                                <div class="block" <?php if($data->p_ep_role == 'MOF_SUPPLIER_ADMIN'): ?>style="background-color: inherit;"<?php endif; ?>>
                                                    <div class="row">
                                                        <?php if($data->is_activate_key == true): ?>
                                                        <div class="col-sm-12"  class="text-left">
                                                            <div class="notify-alert alert alert-success alert-dismissable">
                                                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                                                <h4><i class="fa fa-exclamation-triangle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4> 
                                                                Sila semak <a href="javascript:void(0)" class="alert-link">e-mel <?php echo e($data->p_email); ?> di INBOX / SPAM</a> untuk aktifkan login ID.
                                                                <br /> <br />
                                                                    <strong>Link Activation </strong> :  <a target="_blank" href="<?php echo e($data->link); ?>"><?php echo e($data->link); ?></a> <br />
                                                                    <strong>Activation Key </strong> :  <?php echo e($data->activation_key); ?><br />
                                                                    <strong>Status Success Send </strong> :  <?php echo e($data->is_sent); ?> <br />
                                                                    <strong>Last Date Changed </strong> :  <?php echo e($data->activation_changed_date); ?> <br />

                                                            </div>

                                                        </div>
                                                        <?php endif; ?>

                                                        <div class="col-sm-4">
                                                            <div class="block">
                                                                <div class="block-title">
                                                                    <h2>Maklumat User Login  </h2>
                                                                </div>
                                                                <address class="text-left">
                                                                    <strong>User ID </strong> : <?php echo e($data->user_id); ?><br />
                                                                    <strong>Login ID </strong> : <?php echo e($data->login_id); ?><br />
                                                                    <strong>Email </strong> : <?php echo e($data->email); ?><br />
                                                                    <strong>ICNO </strong> : <?php echo e($data->identification_no); ?><br />
                                                                    <strong>Mobile </strong> : <?php echo e($data->mobile_country); ?><?php echo e($data->mobile_area); ?><?php echo e($data->mobile_no); ?><br />
                                                                    <strong>Record Status </strong> : <?php echo e($data->u_record_status); ?><br />
                                                                    <strong>Last Login Date </strong> : <?php echo e($data->login_date); ?><br />
                                                                    <strong>Last Date Changed </strong> :  <?php echo e($data->changed_date); ?> <br />

                                                                    <br />
                                                                    <strong>Peranan</strong> : <br/>
                                                                    <?php if($data->roles && count($data->roles) > 0 ): ?>
                                                                        <?php $__currentLoopData = $data->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <?php echo e($role->role_code); ?> <br/>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php else: ?>
                                                                    Tiada
                                                                    <?php endif; ?>
                                                                </address>
                                                            </div>
                                                        </div>

                                                         <div class="col-sm-4">
                                                            <div class="block">
                                                                <div class="block-title">
                                                                    <h2>Maklumat Diri  Staf Syarikat </h2>
                                                                </div>
                                                                <address  class="text-left">
                                                                    <strong>Personal ID </strong> : <?php echo e($data->personnel_id); ?><br />
                                                                    <strong>Designation </strong> : <?php echo e($data->p_designation); ?><br />
                                                                    <strong>Email </strong> : <?php echo e($data->p_email); ?><br />
                                                                    <strong>Role </strong> : <?php echo e($data->p_ep_role); ?><br />
                                                                    <strong>Mobile </strong> : <?php echo e($data->p_mobile_country); ?><?php echo e($data->p_mobile_area); ?><?php echo e($data->p_mobile_no); ?><br />
                                                                    <strong>SoftCert Status </strong> : <?php echo e($data->p_is_softcert); ?><br />
                                                                    <strong>Is Equity Owner? </strong> : <?php echo e($data->is_equity_owner); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Is Contract Signer? </strong> : <?php echo e($data->is_contract_signer); ?><br />
                                                                    <strong>Is Authorized? </strong> : <?php echo e($data->is_authorized); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Is Contact Person? </strong> : <?php echo e($data->is_contact_person); ?><br />
                                                                    <strong>Is Management? </strong> : <?php echo e($data->is_mgt); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Is Director? </strong> : <?php echo e($data->is_director); ?><br />
                                                                    <strong>Is Bumi? </strong> : <?php echo e($data->is_bumi); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                    <strong>Record Status </strong> : <?php echo e($data->p_record_status); ?><br />
                                                                    <strong>Last Date Changed </strong> :  <?php echo e($data->p_changed_date); ?> <br />
                                                                    <strong>Rev. No. </strong> :  <?php echo e($data->p_rev_no); ?> <br />
                                                                </address>
                                                            </div>
                                                         </div>

                                                        <?php if(count($data->listSoftCert ) > 0): ?>
                                                        <div class="col-sm-4">
                                                            <div class="block softcert">
                                                                <div class="block-title">
                                                                    <h2>SoftCert Request</h2>
                                                                </div>
                                                                
                                                                
                                                                <a href='#modal-osbdetail-spki'
                                                                    class='pull-left modal-list-data-action'
                                                                    data-toggle='modal'
                                                                    data-url='/find/success-signing/<?php echo e($data->identification_no); ?>/SPKI'
                                                                    data-title='Last Successful Signing' 
                                                                    style="color:darkblue;font-weight: bolder;text-decoration: underline;" >
                                                                    <i class="fa fa-send"></i>  
                                                                    Last Successful Signing
                                                                </a>
                                                                <br /><br />
                                                                    
                                                                <?php $__currentLoopData = $data->listSoftCert; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sCert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <address  class="text-left ">
                                                                    <strong>SoftCert Request ID </strong> : <?php echo e($sCert->softcert_request_id); ?><br /> 
                                                                    <?php if($sCert->is_trustgate == true): ?>
                                                                    <a target='_blank'
                                                                       style="color:darkblue;font-weight: bolder;text-decoration: underline;"
                                                                       href="https://digitalid.msctrustgate.com/nextgenEP/ePRA/migrate/detail_migrate?epNo=<?php echo e($sCert->ep_no); ?>&icNo=<?php echo e($data->p_identification_no); ?>" >
                                                                        Check TrustGate (<?php if($sCert->is_trustgate_data == true): ?>Expiry on  <?php echo e($sCert->trustgate_expired_date); ?><?php endif; ?>)</a><br />
                                                                    <?php endif; ?>
                                                                    <strong>Record Status </strong> : <?php echo e($sCert->record_status); ?><br />
                                                                    <strong>Date Created </strong> :  <?php echo e($sCert->created_date); ?> <br />
                                                                    <strong>Last Date Changed </strong> :  <?php echo e($sCert->changed_date); ?> <br />
                                                                    <strong>Using Free SoftCert</strong> :  <?php echo e($sCert->is_free); ?> <br />
                                                                    
                                                                    <?php if(strlen($sCert->cert_serial_no)>0): ?>
                                                                    <strong>Cert Serial NO. </strong> : <?php echo e($sCert->cert_serial_no); ?><br />
                                                                    <strong>Cert Issuer </strong> : <?php if($sCert->cert_issuer=='T'): ?> TrustGate <?php else: ?> Digicert <?php endif; ?><br />
                                                                    <strong>Cert Valid From </strong> : <?php echo e($sCert->valid_from); ?><br />
                                                                    <strong>Cert Valid To</strong> : <?php echo e($sCert->valid_to); ?><br />
                                                                    <strong>Cert Record Status </strong> : <?php echo e($sCert->pdc_record_status); ?><br />
                                                                    <strong>Cert Last Date Changed </strong> :  <?php echo e($sCert->pdc_changed_date); ?> <br />
                                                                    <?php else: ?>

                                                                        <?php if($sCert->is_success_SPK020 > 0): ?>
                                                                            <a href='#modal-osbdetail-spki'
                                                                               class='modal-list-data-action'
                                                                               data-toggle='modal'
                                                                               data-url='/find/osblog/<?php echo e($sCert->softcert_request_id); ?>/SPK-020/'
                                                                               data-title='Sent Request to SPKI Detail'>
                                                                                <strong style="color:forestgreen;">Successful sent request to SPKI</strong><br />
                                                                            </a>
                                                                        <?php endif; ?>
                                                                        <?php if($sCert->is_success_SPK010 > 0): ?>
                                                                            <a href='#modal-osbdetail-spki'
                                                                               class='modal-list-data-action'
                                                                               data-toggle='modal'
                                                                               data-url='/find/osblog/<?php echo e($sCert->softcert_request_id); ?>/SPK-010/'
                                                                               data-title='Received Update from SPKI Detail'>
                                                                                <strong style="color:forestgreen;">Successfully received update from SPKI</strong><br />
                                                                            </a>
                                                                        <span style="font-style: italic"><?php echo e($sCert->remark); ?></span><br />
                                                                        <?php endif; ?>

                                                                    <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />

                                                                    <?php endif; ?>
                                                                </address>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </div>
                                                        </div>
                                                        <?php endif; ?>

                                                    </div>
                                                </div> 
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                    </tbody>
                                </table>
                            </div>
                            
                            
                            <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            
                            
                            
                            
                               
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>

        <!-- MODAL: KOD BIDANG -->
        <div id="modal-category-code" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i> List Category Code</h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="table-responsive">
                                    <table id="datatable-category-code" class="table table-striped table-vcenter">
                                        <thead>
                                        <tr>
                                            <th class="text-center">No.</th>
                                            <th class="text-left">Category</th>
                                            <th class="text-center">Category Type</th>
                                            <th class="text-left">Officer Decision</th>
                                            <th class="text-center" style="display: none;">Remarks</th>
                                            <th class="text-center">Status</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php $__currentLoopData = $listSupplierCategoryCode; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                                <td class="text-left">
                                                    <?php echo e($data->category_l1_code); ?> - <?php echo e($data->category_l1_name); ?><br/>
                                                    <?php echo e($data->category_l2_code); ?> - <?php echo e($data->category_l2_name); ?><br/>
                                                    <?php echo e($data->category_l3_code); ?> - <?php echo e($data->category_l3_name); ?><br/>
                                                </td>
                                                <td class="text-center"><?php echo e($data->is_special_category === 1 ? "Special" : "Normal"); ?></td>
                                                <td class="text-left">
                                                    <strong>Approved Date :</strong> <?php echo e($data->approved_date); ?><br/>
                                                    <strong>PO :</strong> <?php echo e($data->is_approved_by_po); ?><br/>
                                                    <strong>Approver :</strong> <?php echo e($data->is_approved_by_ap); ?>

                                                </td>
                                                <td class="text-left" style="display: none;">
                                                    <strong>PO :</strong> <?php echo e($data->previous_po_remark); ?><br/>
                                                    <strong>Approver :</strong> <?php echo e($data->previous_ap_remark); ?>

                                                </td>
                                                <td class="text-center"><?php echo e($data->record_status); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>

        <!-- MODAL: SPKI SOFTCERT -->
        <div id="modal-osbdetail-spki" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Title</span></h2>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="osb-detail"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>


<?php $__env->stopSection(); ?>


<?php $__env->startSection('jsprivate'); ?>
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function(){ TablesDatatables.init(); });</script>
    <script>
        var APP_URL = <?php echo json_encode(url('/')); ?>


        $(document).ready(function () {

            $('.softcert').on("click", '.modal-list-data-action', function () {

                $('.spinner-loading').show();
                $('.osb-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data)
                        $('.spinner-loading').hide();
                        $('.osb-detail').html($data).fadeIn();
                    }
                });

            });
        });
    </script>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\user_details_mof.blade.php ENDPATH**/ ?>