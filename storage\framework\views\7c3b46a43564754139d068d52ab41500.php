<?php $__env->startSection('header'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <div class="row">
        <div class="col-lg-4">
            <div id="dash_interface" class="widget">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
        <!-- 
        <div class="col-lg-8">
            <div id="dash_quartz" class="widget">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
        -->
    </div>

    <div class="block">
        <div class="block-title">
            <h2><strong>MMINF</strong> Trigger</h2>
            <div class="block-options pull-right action-today">
                
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="<?php echo e(url('/support/report/log/di-mminf')); ?>/<?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?>" 
                   data-title="List MMINF on pending send to 1GFMAS">List BACKLOG MMINF</a>
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="<?php echo e(url('/support/report/log/task-mminf')); ?>/<?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?>" 
                   data-title="List Action Trigger MMINF Today ">View Today Action</a>
            </div>
        </div>

        <div class="block">
            <div class="block-title">
                <ul id="myTab" class="nav nav-tabs" data-toggle="tabs">
                    <li class="<?php echo e(empty($tabName) || $tabName == 'search-tab-docno' ? 'active' : ''); ?>"><a href="#search-tab-docno">Document No.</a></li>
                    <li class="<?php echo e(!empty($tabName) && $tabName == 'search-tab-reqitemid' ? 'active' : ''); ?>"><a href="#search-tab-reqitemid">Request Item ID</a></li>
                </ul>
            </div>

            <div class="tab-content">
                <div class="tab-pane <?php echo e(empty($tabName) || $tabName == 'search-tab-docno' ? 'active' : ''); ?>" id="search-tab-docno">
                    <form id="form-search-mminf" action="<?php echo e(url("/trigger/gfmas/mminf/search")); ?>" method="post" class="form-horizontal" onsubmit="return true;">
                        <?php echo e(csrf_field()); ?>

                        <input name="_method" id="_method"  type="hidden" value="POST">
                        <input id="searchType" name="searchType" type="hidden" value="search_docno">
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="doc_no">Document No. (PR/CR  PO/CO) <span class="text-danger">*</span></label>
                            <div class="col-md-5">
                                <input id="doc_no" name="doc_no" class="form-control" placeholder="Document No.." type="text" required
                                       <?php if(isset($formSearch, $formSearch["doc_no"])): ?> value="<?php echo e($formSearch["doc_no"]); ?>" <?php endif; ?>>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="item_code">Item Code (18 DIGIT)</label>
                            <div class="col-md-5">
                                <input id="item_code" name="item_code" class="form-control" placeholder="Extension Code.." type="text" 
                                       <?php if(isset($formSearch, $formSearch["item_code"])): ?> value="<?php echo e($formSearch["item_code"]); ?>" <?php endif; ?>>
                            </div>
                        </div>
                        <div class="form-group form-actions">
                            <div class="col-md-9 col-md-offset-3">
                                <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                                <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="tab-pane <?php echo e(!empty($tabName) && $tabName == 'search-tab-reqitemid' ? 'active' : ''); ?>" id="search-tab-reqitemid">
                    <form id="form-search-mminf" action="<?php echo e(url("/trigger/gfmas/mminf/search")); ?>" method="post" class="form-horizontal" onsubmit="return true;">
                        <?php echo e(csrf_field()); ?>

                        <input name="_method" id="_method"  type="hidden" value="POST">
                        <input id="searchType" name="searchType" type="hidden" value="search_reqitemid">
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="req_item_id">Request Item ID <span class="text-danger">*</span></label>
                            <div class="col-md-5">
                                <input id="req_item_id" name="req_item_id" class="form-control" placeholder="Request Item ID.." type="text" required
                                       <?php if(isset($formSearch, $formSearch["req_item_id"])): ?> value="<?php echo e($formSearch["req_item_id"]); ?>" <?php endif; ?>>
                            </div>
                        </div>
                        <div class="form-group form-actions">
                            <div class="col-md-9 col-md-offset-3">
                                <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                                <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php if($result && $result != 'notfound'): ?>
        <div class="block">
            <?php if(isset($formSearch, $formSearch["doc_no"])): ?> 
            <a href="#modal_confirm_trigger" data-toggle="modal" id="trigger-all"
               class="btn btn btn-danger pull-right trigger-all-by-docno" 
               data-url="<?php echo e(url('/trigger/gfmas/mminf/docno/update')); ?>" 
               data-id="<?php echo e($formSearch["doc_no"]); ?>" style="margin:5px;">Trigger ALL Items</a>
            <?php endif; ?>
            <ul class="text-info">
                <li>Trigger will set changed date add 10 minutes from NOW.</li>
                <li>Service MMINF will sent to 1GFMAS after 5 minutes from changed date. </li>
                <li>Click link in Extension Code to view history Item logs. </li>
                
            </ul>
            
            <div class="table-responsive">
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                    <tr>
                        <th class="text-center">Extension Code</th>
                        <th class="text-center">Request Item ID</th>
                        <th class="text-center">Item Name</th>
                        <th class="text-center">Item Description</th>
                        <th class="text-center">UOM Code</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center"></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php $__currentLoopData = $result; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="text-center"><a href="<?php echo e(url("/find/1gfmas/ws")); ?>/<?php echo e($data->extension_code); ?>" target="_blank"><?php echo e($data->extension_code); ?></a></td>
                            <td class="text-center"><?php echo e($data->request_item_id); ?></td>
                            <td class="text-center"><?php echo e($data->item_name); ?></td>
                            <td class="text-center"><?php echo e($data->item_desc); ?></td>
                            <td class="text-center"><?php echo e($data->uom_code); ?></td>
                            <td class="text-center"><?php echo e($data->created_date); ?></td>
                            <td class="text-center td-<?php echo e($data->request_item_id); ?>"><?php echo e($data->changed_date); ?></td>
                            <td class="text-center action_table">
                                <input name="current_changed_date" id="current_changed_date"  type="hidden" value="<?php echo e($data->changed_date); ?>">
                                <input name="doc_no" id="doc_no"  type="hidden" <?php if(isset($formSearch, $formSearch["doc_no"])): ?> value="<?php echo e($formSearch["doc_no"]); ?>" <?php endif; ?>>
                                <input name="item_code" id="item_code"  type="hidden" <?php if(isset($formSearch, $formSearch["item_code"])): ?> value="<?php echo e($formSearch["item_code"]); ?>" <?php endif; ?>>
                                <input name="req_item_id" id="req_item_id"  type="hidden" <?php if(isset($formSearch, $formSearch["req_item_id"])): ?> value="<?php echo e($formSearch["req_item_id"]); ?>" <?php endif; ?>>
                                <input name="tab_name" id="tab_name"  type="hidden" <?php if(isset($tabName)): ?> value="<?php echo e($tabName); ?>" <?php endif; ?>>
                                <div class="btn-group btn-group-xs">
                                    <a class="btn btn-primary action_trigger"  title="" data-original-title="Trigger!"
                                       data-url="<?php echo e(url("/trigger/gfmas/mminf/update")); ?>" 
                                       href="#modal_confirm_trigger" data-toggle="modal" data-id="<?php echo e($data->request_item_id); ?>"><i class="hi hi-transfer"></i> Trigger</a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
    <?php if($result == 'notfound'): ?>
        <div class="block block-alt-noborder full text-center label-primary">
              <span style="color: #FFF;">Tidak dijumpai!</span>
        </div>
    <?php endif; ?>

    <div id="modal_confirm_trigger" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title" id="modal-confirm-title"> Are you sure want to update this item to be triggered? </h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                                <?php echo e(csrf_field()); ?>


                                <div class="form-group">
                                    <label class="col-md-3 control-label" id="label-request-item-id">Request Item ID</label>
                                    <div class="col-md-9">
                                        <input type="hidden" id="trigger_rqid" value="" />
                                        <input type="hidden" id="trigger_rqurl" value="" />
                                        <p id="trigger_rqid_display" class="form-control-static"></p>
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="button" class="btn btn-sm btn-primary action_confirm_trigger"><i class="gi gi-ok_2"></i> Yes</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    <?php echo $__env->make('_shared._modalListLogAction', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- END Content -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function(){ TablesDatatables.init(); });</script>
    <script src="/js/pages/modalListActionLogDatatable.js"></script>
    <script>$(function(){ ModalListActionLogDatatable.init(); });</script>
    <script>
        var APP_URL = <?php echo json_encode(url('/')); ?>


        $('#myTab a').click(function(e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // store the currently selected tab in the hash value
        $("ul.nav-tabs > li > a").on("shown.bs.tab", function(e) {
            var id = $(e.target).attr("href").substr(1);
            window.location.hash = id;
        });

        // on load of the page: switch to the currently selected tab
        var hash = window.location.hash;
        $('#myTab a[href="' + hash + '"]').tab('show');

        //Confirm Trigger Dialogue
        $('td.action_table').on("click",'a.action_trigger', function(){
            $("#modal-confirm-title").text(' Are you sure want to update this item to be triggered? ');
            $("#label-request-item-id").text('Request Item ID');

            var reqItemId = $(this).attr('data-id');
            var urlMminf = $(this).attr('data-url');
            var changedDate = $("#current_changed_date").val();
            var docNo = $("#doc_no").val();
            var itemCode = $("#item_code").val();
            var tabName = $("#tab_name").val();
            console.log("Request Item ID: "+reqItemId);
            console.log("changedDate: "+changedDate);
            console.log("docNo: "+docNo);
            console.log("itemCode: "+itemCode);
            console.log("tabName: "+tabName);
            console.log("urlMminf: "+urlMminf);
            // console.log(docNo+" | "+itemCode+" | "+tabName);
            $("#trigger_rqurl").val(urlMminf);
            $("#trigger_rqid").val(reqItemId);
            $("#trigger_rqid_display").text(reqItemId);
        });
        
        $('div.block').on("click",'.trigger-all-by-docno', function(){
            $("#modal-confirm-title").text(' Are you sure want to update all items in this document number to be triggered? ');
            $("#label-request-item-id").text('Document Number');
             
            var reqItemId = $(this).attr('data-id');
            var urlMminf = $(this).attr('data-url');
            var changedDate = $("#current_changed_date").val();
            var docNo = $("#doc_no").val();
            var itemCode = $("#item_code").val();
            var tabName = $("#tab_name").val();
            console.log("Request Item ID: "+reqItemId);
            console.log("changedDate: "+changedDate);
            console.log("docNo: "+docNo);
            console.log("itemCode: "+itemCode);
            console.log("tabName: "+tabName);
            console.log("urlMminf: "+urlMminf);
            // console.log(docNo+" | "+itemCode+" | "+tabName);
            $("#trigger_rqurl").val(urlMminf);
            $("#trigger_rqid").val(reqItemId);
            $("#trigger_rqid_display").text(reqItemId);
        });
        $('div.form-actions').on("click",'button.action_confirm_trigger', function(){
            
            var reqItemId =$("#trigger_rqid").val();
            var changedDate = $("#current_changed_date").val();
            var trigger_rqurl = $("#trigger_rqurl").val();
            var docNo = $("#doc_no").val();
            var itemCode = $("#item_code").val();
            var tabName = $("#tab_name").val();
            var csrf = $("input[name=_token]").val();

            $('#modal_confirm_trigger').modal('hide');
            $('#wait-modal').modal('toggle');

            $.ajax({
                url: trigger_rqurl,
                method : "POST",
                dataType : "json",
                data : {"_token":csrf,"request_item_id":reqItemId,"changed_date":changedDate,"doc_no":docNo,"item_code":itemCode,"tab_name":tabName,"req_item_id":reqItemId},
                context: document.body
            }).done(function(resp) {
                if(resp.status === 'success'){
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Successfully Triggered!");
  
                    if(resp.hasOwnProperty('total_items')){
                       $("#response-msg").html("Successfully "+resp.total_items+" Items Triggered! Please refresh search.");
                    }    
                    $("td.td-"+reqItemId).addClass("text-success");
                    $("td.td-"+reqItemId).html(resp.value);
                    $('#wait-modal').modal('hide');
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Trigger Failed!.");
                    
                    if(resp.hasOwnProperty('status_error')){
                       $("#response-msg").html("Trigger Failed! Please try again."+resp.status_error);
                    } 
                    
                    $("td.td-"+reqItemId).addClass("text-danger");
                    $('#wait-modal').modal('hide');
                }
            });
        });
        
        

        //DASHBOARD
        /*********************************************************/
        

        //onload quartz widget
        /**
        $.ajax({
            url: APP_URL + '/trigger/gfmas/mminf/quartz',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_quartz').hide().html($data).fadeIn();
            }
        });
        **/
   
        //onload apive diinterfacelog widget
        $.ajax({
            url: APP_URL + '/trigger/gfmas/mminf/diinterfacelog',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_interface').hide().html($data).fadeIn();
            }
        });

        /*********************************************************/

        //interval: quartz widget
        /** 
        setInterval(function () {
            $.ajax({
                url: APP_URL + '/trigger/gfmas/mminf/quartz',
                success: function (data) {
                    $data = $(data);
                    $('#dash_quartz').html($data);
                }
            });
        }, 30000);
        **/
       
       
        //interval: apive diinterfacelog widget
        setInterval(function () {
            $.ajax({
                url: APP_URL + '/trigger/gfmas/mminf/diinterfacelog',
                success: function (data) {
                    $data = $(data);
                    $('#dash_interface').html($data);
                }
            });
        }, 300000);
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\mminf_trigger.blade.php ENDPATH**/ ?>