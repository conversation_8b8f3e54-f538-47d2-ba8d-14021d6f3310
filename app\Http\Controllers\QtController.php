<?php
/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 3/20/2018
 * Time: 10:05 AM
 */

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\SourcingService;
use Carbon\Carbon;

class QtController extends Controller
{
    use SupplierService;
    use SourcingService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function searchPage()
    {
        return view('list_qt_qtno', [
            'qtinfo' => null,
            'listdata' => null,
            'carian' => '',
            'result' => '',
            'type' => 'qtno']);
    }

    public function getSupplierAttendanceByQtNo($qtNo)
    {
        $qtInfo = $this->getScQt($qtNo);
        if ($qtInfo) {
            $list = $this->checkQTSupplierAttendance($qtNo);
            $qtInfo->bsv_date = '-';
            if(count($list) > 0) {
                $qtInfo->bsv_date = $list[0]->bsv_date;
                foreach ($list as $data) {
                    $data->is_authorized = EPService::$IS_CHECKED[$data->is_authorized];
                    $data->is_pre_registered = EPService::$IS_CHECKED[$data->is_pre_registered];
                    $data->is_attended = EPService::$IS_CHECKED[$data->is_attended];
                    $data->is_post_registered = EPService::$IS_CHECKED[$data->is_post_registered];
                    $data->record_status = EPService::$QT_BSV_REGISTRATION_STATUS[$data->record_status];

                    $data->approval_status = '-';
                    if($data->qt_approval_request_id != null) {
                        //dd($data->qt_approval_request_id);
                        $approval = $this->checkQTApproveRequestDetail($data->qt_approval_request_id);
                        $status = $this->getParameterDesc($approval->approver_action_id);
                        $data->approval_status = $status->code_desc;
                    }

                    $qualify = $this->checkDisqualifiedStage($qtNo, $data->supplier_id);
                    if($qualify) {
                        $qualify->disqualified_stage = EPService::$QT_QUALIFY[$qualify->disqualified_stage];
                        $data->qualify = $qualify;
                    }

                }
            }
            return view('list_qt_qtno', [
                'qtinfo' => $qtInfo,
                'listdata' => $list,
                'carian' => $qtNo,
                'type' => 'qtno']);
        }
        return view('list_qt_qtno', [
            'qtinfo' => null,
            'listdata' => null,
            'carian' => $qtNo,
            'result' => 'notfound',
            'type' => 'qtno']);

    }
    
    public function getQtDetails($supplierId, $qtNo, $qtBsvAttendanceId) {
        $qtDetail = $this->checkQTApproveRequest($supplierId, $qtNo);
        $qtDetail->approval_status = '-';
        $qtDetail->approval_request_type_desc = '-';
        $qtDetail->approval_request_type_id = null;
        $qtAppReqId = $qtDetail->qt_approval_request_id;
        if($qtAppReqId) {
            $qtApprovalReq = $this->checkQTApproveRequestDetail($qtAppReqId);
            if($qtApprovalReq) {
                $qtDetail->approvalReqDetail = $qtApprovalReq;
                $qtDetail->approval_status = $qtApprovalReq->approval_status;
                $param = $this->getParameterDesc($qtApprovalReq->approval_request_type_id);
                if($param) {
                    $qtDetail->approval_request_type_id = $qtApprovalReq->approval_request_type_id;
                    $qtDetail->approval_request_type_desc = $param->code_desc;
                }

            }
        }

        $qtBsvAttendance = $this->getQtBsvAttendance($qtBsvAttendanceId);
        if($qtBsvAttendance) {
            $qtDetail->qt_bsv_attendance = $qtBsvAttendance;
        }

        $supplier = $this->getSupplierDetail($supplierId);
        if($supplier) {
            $qtDetail->supplier = $supplier;
        }

        return view('include.qt_more_info', [
            'qtDetail' => $qtDetail]);

    }
    
    public function searchTaklimatOrLawatanTapakByQuotationTender($carian = null)
    {
        $carianTemp = trim(strtoupper($carian));
        $qtInfo = null;
        $list = null;
        if($carianTemp != null){
            $qtInfo = $this->getScQt($carian);
            if($qtInfo){
                $list = $this->getListTaklimatOrLawatanTapakByQuotationTender($carian);
            }
            
        }

        return view('list_qt_lawatan', [
                'qtinfo' => $qtInfo,
                'listdata' => $list,
                'carian' => $carian]);
    }
    
    public function searchProposalSupplierByQuotationTender($carian = null){
        $carianTemp = trim(strtoupper($carian));
        if($carianTemp == null){
            $list = null;
        }else{
           $list = $this->getListProposalSupplierByQuotationTender($carian);
        }
        
        return view('list_qt_proposal', [
            'listdata' => $list,
            'carian' => $carianTemp]);
    }
    
    
    public function searchQTCommitteMembersByQuotationTender($carian = null){
        $carianTemp = trim(strtoupper($carian));
        if($carianTemp == null){
            $list = null;
        }else{
           $list = $this->getListAllCommitteeMembersQT($carian);
        }
        
        return view('list_qt_committees', [
            'listdata' => $list,
            'carian' => $carianTemp]);
    }
    
    
}