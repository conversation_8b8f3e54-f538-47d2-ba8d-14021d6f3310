<?php

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Excel;
use App\Exports\TaskMissingExport;
use App\Imports\TaskMissingUploadImport;

class EpSupportController extends Controller {

    use SupplierService;
    use OSBService;
    use ProfileService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function triggerCaseReportStatistic(){
       $report = new \App\Report\Crm\CaseStatisticReport;
       $dateReport = Carbon::now();
       $report->runToSpecificPerson($dateReport);
       return "Successfully sent email";
    }
    public function triggerCaseReportStatisticUsers(){
       $report = new \App\Report\Crm\CaseStatisticReport;
       $dateReport = Carbon::now();
       $report->runStatisticUsers($dateReport);
       return "Successfully sent email";
    }
    
    public function searchEpTask($carian) {
        $query = DB::connection('mysql_ep_support')->table('ep_task as t');
        $query->join('ep_task_category as tc', 'tc.category_id', '=', 't.category_id');
        $query->where('t.is_deleted',0);
        $query->where(function ($query) use ($carian) {
            $query  ->orWhere('t.case_no', 'like', '%'.$carian.'%')
                    ->orWhere('t.entity_name', 'like', '%'.$carian.'%')
                    ->orWhere('t.description', 'like', '%'.$carian.'%');
        });
        /*
        if ($request->isMethod('post')) {
            if(strlen($request->search_status_id) > 0){
                $query->where('t.status',$request->search_status_id);
            }
            if(strlen($request->search_category_id) > 0){
                $query->where('t.category_id',$request->search_category_id);
            }
        }
         */
        $query->select('t.*','tc.*');
        $query->orderBy('t.status','asc');
        $list = $query->get();

        $query2 = DB::connection('mysql_ep_support')->table('ep_task_category');
        $listCategory = $query2->get();

        return view('list_task', [
            'listdata' => $list,
            'categories' => $listCategory,
            'success' => '',
            'carian' => $carian
        ]);
    }
    
    public function searchEpTask2(Request $request) {
        $query = DB::connection('mysql_ep_support')->table('ep_task as t');
        $query->join('ep_task_category as tc', 'tc.category_id', '=', 't.category_id');
        $query->where('t.is_deleted',0);
        if(strlen($request->search_status_id) > 0){
            $query->where('t.status',$request->search_status_id);
        }
        if(strlen($request->search_category_id) > 0){
            $query->where('t.category_id',$request->search_category_id);
        }
        $query->select('t.*','tc.*');
        $query->orderBy('t.status','asc');
        $list = $query->get();

        $query2 = DB::connection('mysql_ep_support')->table('ep_task_category');
        $listCategory = $query2->get();
        
        return view('list_task', [
            'listdata' => $list,
            'categories' => $listCategory,
            'success' => '',
            'carian' => '',
            'formSearch' => $request->all()
        ]);
    }
    
    public function listEpTask(Request $request) {
        //dd($request->all());
        $successForm = "";
        $this->saveTask($request,$successForm);

        $query = DB::connection('mysql_ep_support')->table('ep_task as t');
        $query->join('ep_task_category as tc', 'tc.category_id', '=', 't.category_id');
        $query->where('t.is_deleted',0);
        $query->where('t.status',0);
        $query->select('t.*','tc.*');
        $query->orderBy('t.status','asc');
        $list = $query->get();

        $query2 = DB::connection('mysql_ep_support')->table('ep_task_category');
        $listCategory = $query2->get();


        return view('list_task', [
            'listdata' => $list,
            'categories' => $listCategory,
            'success' => $successForm,
            'carian' => ''
        ]);
    }

    protected function saveTask(Request $request, &$successForm) {
        
        $messages = [
            'entity_name.required' => 'The company name field is required.',
        ];

        if ($request->isMethod('post')) {

            Validator::make($request->all(), [
                'category_id' => 'required',
                'case_no' => 'required|integer',
                'entity_name' => 'required|max:200',
                'description' => 'required|max:700',
            ],$messages)->validate();

            if($request->task_id && strlen(trim($request->task_id)) > 0){
                DB::connection('mysql_ep_support')
                ->table('ep_task')
                ->where('task_id', $request->task_id)
                ->update([
                    'category_id' => $request->category_id, 
                    'entity_name' => $request->entity_name,
                    'case_no'     => $request->case_no,
                    'case_type'   => $request->ccase_type,
                    'status'     => $request->status_id,
                    'description' => $request->description,
                    'updated_at'  => Carbon::now(),
                    'updated_by'  => Auth::user()->user_name
                    ]);
                $successForm = "success";
                
           }else{
                $checkExist = DB::connection('mysql_ep_support')
                            ->table('ep_task')
                            ->where('case_no', $request->case_no)
                            ->count();
                if($checkExist == 0){        
                    DB::connection('mysql_ep_support')
                    ->insert('insert into ep_task 
                        (category_id, entity_name,case_no,case_type,description,status,created_at,created_by) 
                        values (?, ?, ?, ?, ?, ?, ?, ?)', 
                        [   
                            $request->category_id, 
                            $request->entity_name,
                            $request->case_no,
                            $request->case_type,
                            $request->description,
                            0, //As default (pending)
                            Carbon::now(),
                            Auth::user()->user_name  ]);
                    $successForm = "success";
                }else{
                    $successForm = "failed";
                }
           }        
        }

        if ($request->isMethod('put')) {
            Validator::make($request->all(), [
                'category_id' => 'required',
                'case_no' => 'required|max:10',
                'entity_name' => 'required|max:200',
                'description' => 'required|max:700',
                'resolution' => 'required|max:700',
                'task_id' => 'required',
            ],$messages)->validate();

            DB::connection('mysql_ep_support')
                ->table('ep_task')
                ->where('task_id', $request->task_id)
                ->update([
                    'resolution' => $request->resolution,
                    'status' => 1,
                    'completed_at'  => Carbon::now(),
                    'completed_by'  => Auth::user()->user_name
                    ]);
            $successForm = "success";

        }
    }
    
    public function getTask($taskID) {
        $query = DB::connection('mysql_ep_support')->table('ep_task as t');
        $query->join('ep_task_category as tc', 'tc.category_id', '=', 't.category_id');
        $query->where('t.is_deleted',0);
        $query->where('t.task_id',$taskID);
        $query->select('t.*','tc.*');
        $result = $query->first();

        return json_encode($result);
    }
    
    public function getDetailCaseCRM($caseno) {
        //return 'ok';
        $query = DB::table('cases as a');
        $query->join('cases_cstm as b', 'b.id_c', '=', 'a.id');
        $query->join('accounts as c', 'c.id', '=', 'a.account_id');
        $query->where('a.case_number',$caseno);
        $query->select('a.id AS case_id','a.name','a.case_number','a.status','a.description');
        $query->addSelect('b.category_c','b.request_type_c','c.name AS account_name');
        $query->addSelect('c.mof_no','c.registration_no');
        $result = $query->first();
        return json_encode($result);

    }
    
    protected function defaultListEpTaskMissing($successForm){
        $query = DB::connection('mysql_ep_support')->table('ep_task_missing as t');
        $query->where('t.is_deleted',0);
        $query->whereNotIn('t.process_status',['55']);
        $query->select('t.*');
        $query->orderBy('t.process_status','asc');
        $list = $query->get();

        return view('list_task_missing', [
            'listdata' => $list,
            'success' => $successForm,
            'carian' => ''
        ]);
    }
    
    public function listEpTaskMissing(Request $request) {
        //dd($request->all());
        $successForm = "";
        $this->saveTaskMissing($request,$successForm);
        return $this->defaultListEpTaskMissing($successForm);

    }
    
    public function searchEpTaskMissing(Request $request) {
        //dd($request->all()); 
        $query = DB::connection('mysql_ep_support')->table('ep_task_missing as t');
        $query->where('t.is_deleted',0);
        
        if(strlen($request->search_status_id) > 0){
            $query->where('t.process_status',$request->search_status_id);
        }
        
        if($request->search_module != null){
            $query->where('t.module',$request->search_module);
        }
        if($request->search_case_status != null){
            $query->where('t.case_status',$request->search_case_status);
        }
        
        $query->select('t.*');
        $query->orderBy('t.process_status','asc');
        $list = $query->get();
        
        return view('list_task_missing', [
            'listdata' => $list,
            'success' => '',
            'carian' => '',
            'formSearch' => $request->all()
        ]);
    }
    
    public function searchInputEpTaskMissing($carian) {
        //dd($request->all()); 
        $query = DB::connection('mysql_ep_support')->table('ep_task_missing as t');
        $query->where('t.is_deleted',0);
        
        $query->where(function ($query) use ($carian) {
            $query  ->orWhere('t.case_no', 'like', '%'.$carian.'%')
                    ->orWhere('t.doc_no', 'like', '%'.$carian.'%')
                    ->orWhere('t.batch', 'like', '%'.$carian.'%')
                    ->orWhere('t.case_status', 'like', '%'.$carian.'%')
                    ->orWhere('t.resolution', 'like', '%'.$carian.'%');
        });
        
        $query->select('t.*');
        $query->orderBy('t.process_status','asc');
        $list = $query->get();
        
        return view('list_task_missing', [
            'listdata' => $list,
            'success' => '',
            'carian' => ''
        ]);
    }
    
    protected function saveTaskMissing(Request $request, &$successForm) {
        
        $messages = [
            'entity_name.required' => 'The company name field is required.',
        ];

        if ($request->isMethod('post')) {

            Validator::make($request->all(), [
                'case_no' => 'required|integer',
                'case_status' => 'required',
                'doc_no' => 'required',
                'module_task' => 'required',
            ],$messages)->validate();

            if($request->task_id && strlen(trim($request->task_id)) > 0){
                DB::connection('mysql_ep_support')
                ->table('ep_task_missing')
                ->where('task_id', $request->task_id)
                ->update([
                    'case_no'     => $request->case_no,
                    'case_status'   => $request->case_status,
                    'batch'     => $request->batch,
                    'doc_no' => $request->doc_no,
                    'module' => $request->module_task,
                    'process_status' => $request->process_status,
                    'resolution' => $request->resolution,
                    'updated_at'  => Carbon::now(),
                    'updated_by'  => Auth::user()->user_name
                    ]);
                $successForm = "success";
                
           }else{
               
                $checkExist = $this->checkRecordTaskMissing($request->case_no);
                if($checkExist == 0){
                    $caseObj = $this->getCaseDetail($request->case_no);
                    if($caseObj != null){
                        DB::connection('mysql_ep_support')
                                ->insert('insert into ep_task_missing 
                                (   case_no,
                                    case_status,
                                    case_created,
                                    batch,
                                    doc_no,
                                    module,
                                    problem,
                                    process_status,
                                    created_at,
                                    created_by ) 
                                values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', 
                                [   
                                    $request->case_no, 
                                    $request->case_status,
                                    $caseObj->date_entered,
                                    $request->batch,
                                    $request->document_number,
                                    $request->module_task,
                                    $caseObj->description,
                                    '00',
                                    Carbon::now(),
                                    Auth::user()->user_name,
                                        ]);
                        $successForm = "success";
                    }else{
                        $successForm = "case-not-exist";
                    }
                }else{
                    $successForm = "duplicate";
                }
           }        
        }

        if ($request->isMethod('put')) {
            Validator::make($request->all(), [
                'case_no' => 'integer',
                'case_status' => 'required',
                'doc_no' => 'required',
                'resolution' => 'required',
                'module_task' => 'required',
                'task_id' => 'required',
            ],$messages)->validate();

            DB::connection('mysql_ep_support')
                ->table('ep_task_missing')
                ->where('task_id', $request->task_id)
                ->update([
                    'resolution' => $request->resolution,
                    'process_status' => '55',
                    'completed_at'  => Carbon::now(),
                    'completed_by'  => Auth::user()->user_name
                    ]);
            $successForm = "success";

        }
    }
    
    public function downloadTaskMissing(){

        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes

        $dateNow = Carbon::now()->format('Ymd-hi');
        $fileName = 'TaskMissing_'.$dateNow;

        // Store the file using Laravel Excel 3.x
        Excel::store(new TaskMissingExport, 'exports/task_missing/'.$fileName.'.xlsx');

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $fullPath = storage_path('app/exports/task_missing/'.$fileName.'.xlsx');
        return response()->download($fullPath, $fileName.'.xlsx', $headers);
        
    }
    
    public function uploadReport(Request $request) {
        $success = '';
        if ($request->isMethod("POST")) {
            //return $request->all();

            Validator::make($request->all(), [
                'file-input-excel' => 'required|mimes:xlsx,xls',
            ])->validate();

            $directoryPath = "imports/task_missing";
            $fileNameExcel = 'TaskMissing_' . Carbon::now()->format('d-M-y-Hi');

            $file = $request->file('file-input-excel');
            $ext_file = $file->guessClientExtension();
            $file->storeAs($directoryPath,$fileNameExcel.'.'.$ext_file);
            
            // Use Laravel Excel 3.x Import class
            $import = new TaskMissingUploadImport();
            Excel::import($import, $file);

            // Get counters from the import class
            $counters = $import->getCounters();
            $invalidCounter = $counters['invalid'];
            $successCounter = $counters['success'];
            $duplicateCounter = $counters['duplicate'];
            $updateCounter = $counters['update'];

            // Remove the old Excel::load logic - replaced by import class
            /*Excel::load($file, function($reader) use (&$invalidCounter,&$successCounter,&$duplicateCounter,&$updateCounter) {
                $reader->each(function($row) use (&$invalidCounter,&$successCounter,&$duplicateCounter,&$updateCounter)  {

                    $caseNumber = intval($row->crm_case_no);
                    $case = DB::table('cases')->where('case_number',$caseNumber)->first();
                    if($case){
                        
                        $problem = $case->description;

                        $statusTask = trim($row->status);
                        $resolution = trim($row->remarks);
                        foreach (EPService::$TASK_MISSING_STATUS as $key => $value) {
                            if($value == strtoupper($statusTask)){
                                $statusTask = $key;
                                break;
                            }
                        }
                        if($statusTask == null){
                           $statusTask = '00'; //Pending In Action
                        }
                        $is_case_closed = 0;
                        if($case->status == 'Closed_Closed' || $case->status == 'Open_Resolved'){
                            $is_case_closed = 1;
                            if($statusTask != '55'){
                                $statusTask = '55'; //Done
                                $resolution = $resolution.'&#13;&#10;Updated &#13;&#10;Case CRM resolved on '.$case->date_modified;
                            }
                        }
                        $check = DB::connection('mysql_ep_support')->table('ep_task_missing')
                                ->where('case_no',$row->crm_case_no)->count();
                        if($check == 0){
                            DB::connection('mysql_ep_support')
                            ->insert('insert into ep_task_missing 
                            (   case_no,
                                case_status,
                                case_created,
                                batch,
                                doc_no,
                                module,
                                process_status,
                                composite_instance_id,
                                resolution,
                                problem,
                                is_case_closed,
                                created_at,
                                created_by ) 
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', 
                            [   
                                $row->crm_case_no, 
                                $case->status,
                                $case->date_entered,
                                $row->batch,
                                $row->document_number,
                                trim($row->module),
                                $statusTask,
                                $row->composite_instance_id,
                                $resolution,
                                $problem,
                                $is_case_closed,
                                Carbon::now(),
                                Auth::user()->user_name
                                    ]);
                            $successCounter++;
                        }else{
                            if($statusTask == '55'){
                                $taskMissObj = DB::connection('mysql_ep_support')->table('ep_task_missing')
                                    ->where('case_no',$row->crm_case_no)->first();
                                if($taskMissObj->process_status != '55'){
                                    
                                    $module = $taskMissObj->module;
                                    if($module == ''){
                                        $module = trim($row->module);
                                    }
                                    
                                    $batch = $taskMissObj->batch;
                                    if($batch == ''){
                                        $batch = trim($row->batch);
                                    }
                                    
                                    DB::connection('mysql_ep_support')
                                    ->table('ep_task_missing')
                                    ->where('task_id', $taskMissObj->task_id)
                                    ->update([
                                        'module'    => $module,
                                        'batch'     => $batch,
                                        'case_status' => $case->status,
                                        'is_case_closed' => $is_case_closed,
                                        'resolution' => $resolution,
                                        'process_status' => $statusTask,
                                        'completed_at'  => Carbon::now(),
                                        'completed_by'  => Auth::user()->user_name
                                        ]);
                                    
                                    $updateCounter++;
                                }else{
                                    $duplicateCounter++;
                                }
                            }else{
                                $duplicateCounter++;
                            }
                        }
                    }else{
                        $invalidCounter++;
                    }
                });
            }); */
            $success  = 'Tasks Created: '.$successCounter.', Tasks Updated : '.$updateCounter.', Tasks Duplicate Found: '.$duplicateCounter.', Tasks Invalid: '.$invalidCounter;
        }
        
        return $this->defaultListEpTaskMissing($success);
    }
    
    protected function checkRecordTaskMissing ($caseNo){
        return DB::connection('mysql_ep_support')->table('ep_task_missing')
                            ->where('case_no',$caseNo)->count();
    }
    
    protected function getCaseDetail($caseNo){
        $query = DB::table('cases as a');
        $query->join('cases_cstm as b', 'b.id_c', '=', 'a.id');
        $query->where('a.case_number',$caseNo);
        $query->select('a.*','b.*');
        return $query->first();
    }
    
    public function getCRMCaseAndCheckTaskMissing($caseNo){
        $collect = collect([]);
        $checkExist = $this->checkRecordTaskMissing($caseNo);
        $collect->put("is_exist_task", $checkExist);
        $list = $this->getDetailCaseCRM($caseNo);
        $collect->put("case",$list);
        return $collect;
    }
    
    
    public function getTaskMissing($taskID) {
        $query = DB::connection('mysql_ep_support')->table('ep_task_missing as t');
        $query->where('t.is_deleted',0);
        $query->where('t.task_id',$taskID);
        $query->select('t.*');
        $result = $query->first();

        return json_encode($result);
    }
    
    
    public function getListDetailInfoLogHTML($type,$search) {
        $dataList = array();
        if($type == 'bank-pembekal-tidak-wujud'){
            $dataList = DB::connection('oracle_nextgen_rpt')->select(
                    "SELECT 
                    'Bank Pembekal tidak wujud di 1GFMAS' as ERROR_GFM120,
                    SYSDATE-1 as DATE_ERROR_HAPPEN,
                    A.DOC_TYPE as PRCR_DOC_TYPE, A.DOC_NO as PRCR_DOC_NO , 
                    B.DOC_TYPE as POCO_DOC_TYPE, B.DOC_NO as POCO_DOC_NO,
                    D.INVOICE_NO,
                    E.ORG_NAME as PTJ_NAME, E.ORG_CODE as PTJ_CODE, 
                    C.COMPANY_NAME,C.EP_NO ,
                    D.ACCOUNT_NO as ACCOUNT_NO_IN_INVOICE,D.PAYMENT_BANK_NAME AS BANK_NAME_IN_INVOICE ,
                    (select count(*) from OSB_LOGGING where service_code = 'GFM-010' AND REMARKS_3 = C.EP_NO  and ROWNUM < 2 )   as TOTAL_SEND_APIVE ,
                    (select CONCAT(a.ACCOUNT_NO, CONCAT( ' - ', b.BANK_CODE))  as BANK_PAYLOAD from SM_SUPPLIER_BANK a, PM_FINANCIAL_SVC b  
                    where  a.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID 
                    and a.appl_id = C.LATEST_APPL_ID  
                    and rev_no in (select max(rev_no) from SM_SUPPLIER_BANK  b where b.appl_id = a.appl_id) AND ROWNUM < 2 ) as LATEST_BANK_NO_IN_SUPPLIER
                    from FL_FULFILMENT_REQUEST A 
                  INNER JOIN FL_FULFILMENT_ORDER  B ON A.FULFILMENT_REQ_ID = B.FULFILMENT_REQ_ID 
                  INNER JOIN SM_SUPPLIER C ON A.SUPPLIER_ID = C.SUPPLIER_ID 
                  INNER JOIN PM_ORG_VALIDITY E ON E.ORG_PROFILE_ID = A.CREATED_ORG_PROFILE_ID AND E.RECORD_STATUS = 1 
                  INNER JOIN FL_INVOICE D ON D.FULFILMENT_REQ_ID = A.FULFILMENT_REQ_ID  AND D.FULFILMENT_ORDER_ID = B.FULFILMENT_ORDER_ID     AND D.RECORD_STATUS = 1 
                  WHERE B.DOC_NO = ? or A.DOC_NO = ?  ", array($search,$search));
        }else if($type == 'invois-telah-wujud'){
            $dataList = DB::connection('oracle_nextgen_rpt')->select(
                    "SELECT 
                    'Invois telah wujud di 1GFMAS' as ERROR_GFM120,
                    SYSDATE-1 as DATE_ERROR_HAPPEN,
                    A.DOC_TYPE as PRCR_DOC_TYPE, A.DOC_NO as PRCR_DOC_NO , 
                    B.DOC_TYPE as POCO_DOC_TYPE, B.DOC_NO as POCO_DOC_NO,
                    D.INVOICE_NO,
                    E.ORG_NAME as PTJ_NAME, E.ORG_CODE as PTJ_CODE, 
                    C.COMPANY_NAME,C.EP_NO ,
                    D.ACCOUNT_NO as ACCOUNT_NO_IN_INVOICE,D.PAYMENT_BANK_NAME AS BANK_NAME_IN_INVOICE ,
                    (select CONCAT(a.ACCOUNT_NO, CONCAT( ' - ', b.BANK_CODE))  as BANK_PAYLOAD from SM_SUPPLIER_BANK a, PM_FINANCIAL_SVC b  
                    where  a.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID 
                    and a.appl_id = C.LATEST_APPL_ID  
                    and rev_no in (select max(rev_no) from SM_SUPPLIER_BANK  b where b.appl_id = a.appl_id) AND ROWNUM < 2 ) as LATEST_BANK_NO_IN_SUPPLIER
                    from FL_FULFILMENT_REQUEST A 
                  INNER JOIN FL_FULFILMENT_ORDER  B ON A.FULFILMENT_REQ_ID = B.FULFILMENT_REQ_ID 
                  INNER JOIN SM_SUPPLIER C ON A.SUPPLIER_ID = C.SUPPLIER_ID 
                  INNER JOIN PM_ORG_VALIDITY E ON E.ORG_PROFILE_ID = A.CREATED_ORG_PROFILE_ID AND E.RECORD_STATUS = 1 
                  INNER JOIN FL_INVOICE D ON D.FULFILMENT_REQ_ID = A.FULFILMENT_REQ_ID  AND D.FULFILMENT_ORDER_ID = B.FULFILMENT_ORDER_ID     AND D.RECORD_STATUS = 1 
                  WHERE B.DOC_NO = ? or A.DOC_NO = ?  ", array($search,$search));
        }else{
           return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type is not define properly!  $type</th>
                            </tr>
                        </thead>"; 
        }
        
        return $this->populateOneRecordDataTableHTML($dataList);
    }
    public function getListActionLogHTML($type,$date) {
        $dataList = array();
        if($type == 'task'){
            $query = DB::connection('mysql_ep_support')->table('ep_task as t');
            $query->join('ep_task_category as tc', 'tc.category_id', '=', 't.category_id');
            $query->where('t.is_deleted',0);
            $query->where(function ($query) use ($date) {
                $query  ->orWhereDate('t.created_at', '=', $date)
                        ->orWhereDate('t.updated_at', '=', $date)
                        ->orWhereDate('t.completed_at', '=', $date);
            });
            $query->select('t.case_no','tc.category_name','status');
            $query->addSelect(DB::raw("concat(created_at,' by ',created_by) as Created"));
            $query->addSelect(DB::raw("concat(updated_at,' by ',updated_by) as Updated"));
            $query->addSelect(DB::raw("concat(completed_at,' by ',completed_by) as Completed"));
            $dataList = $query->get();
            foreach ($dataList as $obj){
                if($obj->status){
                   $obj->status = EPService::$TASK_STATUS[$obj->status] ;
                }
            }
        }else if($type == 'task-missing'){
            $query = DB::connection('mysql_ep_support')->table('ep_task_missing');
            $query->where('is_deleted',0);
            $query->where(function ($query) use ($date) {
                $query  ->orWhereDate('created_at', '=', $date)
                        ->orWhereDate('updated_at', '=', $date)
                        ->orWhereDate('completed_at', '=', $date);
            });
            $query->select('case_no','case_status','process_status');
            $query->addSelect(DB::raw("concat(created_at,' by ',created_by) as Created"));
            $query->addSelect(DB::raw("concat(updated_at,' by ',updated_by) as Updated"));
            $query->addSelect(DB::raw("concat(completed_at,' by ',completed_by) as Completed"));
            $dataList = $query->get();
            foreach ($dataList as $obj){
                if($obj->process_status){
                   $obj->process_status = EPService::$TASK_MISSING_STATUS[$obj->process_status] ;
                }
            }
        }else if($type == 'task-sync-payment'){ // Refer to Server 1GFMAS  Folder IN
            $query = DB::connection('mysql_ep_support')->table('ep_payment_failed');
            $query->where(function ($query) use ($date) {
                $query  ->orWhereDate('created_at', '=', $date);
            });
            $query->select('company_name','ep_no','order_id',
                    'bill_type','bill_no',
                    'payment_amt');
            $query->addSelect(DB::raw("concat(created_at,' by ',created_by) as Created"));
            $dataList = $query->get();
        }else if($type == 'task-apive'){ // Refer to Server 1GFMAS  Folder OUT
            $query = DB::connection('mysql_ep_support')->table('ep_action_log');
            $query->where('action_name','TriggerAPIVE');
            $query->where(function ($query) use ($date) {
                $query  ->orWhereDate('created_at', '=', $date)
                        ->orWhereDate('updated_at', '=', $date);
            });
            $query->select('action_name','action_status');
            $query->addSelect(DB::raw("concat(created_at,' by ',created_by) as Created"));
            $query->addSelect(DB::raw("concat(updated_at,' by ',updated_by) as Updated"));
            $query->addSelect('action_parameter');
            $dataList = $query->get();
        }else if($type == 'task-mminf'){ 
            $query = DB::connection('mysql_ep_support')->table('ep_action_log');
            $query->where('action_name','TriggerMMINFID');
            $query->where(function ($query) use ($date) {
                $query  ->orWhereDate('created_at', '=', $date)
                        ->orWhereDate('updated_at', '=', $date);
            });
            $query->select('action_name','action_status');
            $query->addSelect(DB::raw("concat(created_at,' by ',created_by) as Created"));
            $query->addSelect(DB::raw("concat(updated_at,' by ',updated_by) as Updated"));
            $query->addSelect('action_parameter');
            $dataList = $query->get();
        }else if($type == 'di-mminf'){ 
            $query = DB::connection('oracle_nextgen_rpt')->table('di_mminf');
            $query->where('is_sent',0);
//            $query->where(function ($query) use ($date) {
//                $query  ->orWhereDate('created_date', '=', $date)
//                        ->orWhereDate('changed_date', '=', $date);
//            });
            $query->select('created_date','material_code as item_kod','action_code','base_uom','alt_uom','is_sent');
            $dataList = $query->get();
        }else if($type == 'item-codification'){ 
            $dataList = DB::connection('oracle_nextgen_rpt')->select(
                    "SELECT TO_CHAR(CWS.CREATED_DATE,'YYYY-MM-DD') AS CREATED_DATE_STATUS,
                        U.IDENTIFICATION_NO,U.USER_NAME as NAME,
                        D.STATUS_NAME,
                        COUNT(*) AS TOTAL 
                        FROM CM_REQUEST_CODE CRC , CM_WORKFLOW_STATUS CWS, PM_STATUS_DESC D , PM_USER U 
                        WHERE CRC.REQUEST_ID = CWS.DOC_ID 
                        AND D.STATUS_ID = CWS.STATUS_ID AND D.LANGUAGE_CODE = 'en' 
                        AND CWS.CREATED_BY = U.USER_ID AND U.ORG_TYPE_ID = '9'
                        AND CRC.RECORD_STATUS = 1 
                        AND TO_CHAR(CWS.CREATED_DATE,'YYYY-MM-DD') = ? 
                        GROUP BY TO_CHAR(CWS.CREATED_DATE,'YYYY-MM-DD'),U.IDENTIFICATION_NO,U.USER_NAME,D.STATUS_NAME 
                        ORDER BY TOTAL DESC,D.STATUS_NAME", array($date));
            
            foreach($dataList as $obj){
                if($obj->identification_no != ''){
                    $urlTaskCodi = url('/find/items/codi-task').'/'.$obj->identification_no;
                    $obj->identification_no = "<a href='$urlTaskCodi' target='_blank' >$obj->identification_no</a>";
                }
            }

        }else if($type == 'patch-data'){ 
            $query = DB::connection('mysql_ep_support')->table('ep_action_log');
            $query->where('action_name','PatchData');
            $query->where(function ($query) use ($date) {
                $query  ->orWhereDate('created_at', '=', $date)
                        ->orWhereDate('updated_at', '=', $date);
            });
            $query->select('action_name','action_status','action_parameter');
            $query->addSelect(DB::raw("concat(created_at,' by ',created_by) as Created"));
            $query->addSelect('action_data');
            $dataList = $query->get();
        }else{
           return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type is not define properly!  $type</th>
                            </tr>
                        </thead>"; 
        }
        
        return $this->populateListUserStatisticActionHTML($dataList);
    }

    protected function populateOneRecordDataTableHTML($dataList){
        //$keys = collect()
        $html =    "<thead>";
        $html = $html."<tr>"
                        . "<td class='text-right'><strong>LABEL</strong></td>"
                        . "<td class='text-left'>VALUE</td>"
                        . "</tr>";
        $html = $html."</thead>";
        $html = $html."<tbody>";
        if(count($dataList) > 0){
            $objData = $dataList[0];
            $keys = array_keys((array)$objData);
            for ($index = 0; $index < count($keys); $index++) {
                if(strtoupper($keys[$index]) == 'EP_NO'){
                   $html = $html."<tr>"
                        . "<td class='text-right'><strong>".strtoupper($keys[$index])."</strong></td>"
                        . "<td class='text-left'><a href='".url('/find/epno')."/".$objData->$keys[$index]."' target='_blank'>".$objData->$keys[$index]."</a></td>"
                        . "</tr>"; 
                }else{
                    $html = $html."<tr>"
                        . "<td class='text-right'><strong>".strtoupper($keys[$index])."</strong></td>"
                        . "<td class='text-left'>".$objData->$keys[$index]."</td>"
                        . "</tr>";
                }
                
            }
        }else{
           $html = $html."<tr><td class='text-left'><strong>Info</strong></td></tr>"; 
        }
        $html = $html."</tbody>";
        return $html;
    }
    
    protected function populateListUserStatisticActionHTML($dataList){
        //$keys = collect()
        $html =    "<thead>
                        <tr>";
        if(count($dataList) > 0){
        foreach ($dataList as $obj) {
            $keys = array_keys((array)$obj);
            for ($index = 0; $index < count($keys); $index++) {
                $html = $html."<th class='text-left'><strong>".$keys[$index]."</strong></th>";
            }
            break;
        }
        }else{
           $html = $html."<th class='text-left'><strong>Info</strong></th>"; 
        }
        $html = $html.   " </tr>
                    </thead>";
        $html = $html."<tbody>";

        if(count($dataList) > 0){
            foreach ($dataList as $obj){
                $data = "<tr>";
                $keys = array_keys((array)$obj);
                for ($index = 0; $index < count($keys); $index++) {
                    $data = $data."<td class='text-left'>".$obj->$keys[$index]."</td>";
                }
                $data = $data."</tr>";
                $html = $html.$data;
            }
        }else{
            $data = "<tr>";
            $data = $data."<td class='text-left'>Tiada rekod</td>";
            $data = $data."</tr>";
            $html = $html.$data;
        }
        $html = $html."</tbody>";
        return $html;
    }
    
}
