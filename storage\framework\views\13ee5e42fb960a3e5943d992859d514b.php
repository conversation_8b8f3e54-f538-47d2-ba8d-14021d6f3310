<?php $__env->startSection('header'); ?>
    <!-- Search Form -->
    <form id="carianform" action="<?php echo e(url('/find/qt')); ?>/<?php echo e($type); ?>/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ...">
        </div>
    </form>
    <!-- END Search Form -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if($qtinfo == null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <?php if($result == 'notfound'): ?>
                        <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                        <small>Rekod tidak dijumpai!</small>
                    <?php else: ?>
                        <?php if($type == 'qtno'): ?>
                            <i class="gi gi-search"></i>Carian Taklimat/Lawatan Tapak (Quotation/Tender)<br>
                            <small>Masukkan QT No. pada carian diatas...</small>
                        <?php endif; ?>
                    <?php endif; ?>
                </h1>
            </div>
        </div>
    <?php endif; ?>
    <?php if($qtinfo): ?>
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title  panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Quotation/Tender : <?php echo e($qtinfo->qt_no); ?></strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-9">
                        <h6><strong><?php echo e($qtinfo->qt_title); ?></strong></h6>
                        <address>
                            <strong>QT No.</strong> : <?php echo e($qtinfo->qt_no); ?><br />
                            <strong>File No.</strong> : <?php echo e($qtinfo->file_no); ?><br />
                            <strong>Publish Date</strong> : <?php echo e($qtinfo->publish_date); ?><br />
                            <strong>Proposal Start Date</strong> : <?php echo e($qtinfo->proposal_start_date); ?><br />
                            <strong>Closing Date</strong> : <?php echo e($qtinfo->closing_date); ?><br />
                            <strong>BSV Date</strong> : <?php echo e($qtinfo->bsv_date); ?><br />
                        </address>
                    </div>
                    <div class="col-md-3">
                        <div class="table-responsive">
                            <table id="general-table" class="table table-vcenter table-hover table-borderless">
                                <tbody>
                                <tr>
                                    <td class="text-center"><span class="badge label-danger"><strong>B</strong></span></td>
                                    <td class="text-left">Failed to attend briefing and site visit</td>
                                </tr>
                                <tr>
                                    <td class="text-center"><span class="badge label-danger"><strong>C</strong></span></td>
                                    <td class="text-left">Supplier Criteria</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <?php if($listdata): ?>
                        <div class="table-responsive">
                            <table id="attend-list" class="table table-striped table-vcenter">
                                <thead>
                                <tr>
                                    <th class="text-center">No.</th>
                                    <th class="text-center">MOF No.</th>
                                    <th class="text-center">Identification No.</th>
                                    <th class="text-left">Name</th>
                                    
                                    <th class="text-center">Authorized</th>
                                    <th class="text-center">Pre Registered</th>
                                    <th class="text-center">Attended</th>
                                    <th class="text-center">Post Registered</th>
                                    <th class="text-center">Disqualified Stage</th>
                                    
                                    <th class="text-center">Approval Status</th>
                                    <th class="text-center">Info</th>
                                </tr>
                                </thead>

                                <tbody>
                                <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                        <td class="text-center"><?php echo e($data->mof_no); ?></td>
                                        <td class="text-center"><?php echo e($data->ic_passport); ?></td>
                                        <td class="text-left"><?php echo e($data->name); ?></td>
                                        
                                        <td class="text-center"><?php echo $data->is_authorized; ?></td>
                                        <td class="text-center"><?php echo $data->is_pre_registered; ?></td>
                                        <td class="text-center"><?php echo $data->is_attended; ?></td>
                                        <td class="text-center"><?php echo $data->is_post_registered; ?></td>
                                        <td class="text-center"><?php echo $data->qualify->disqualified_stage; ?></td>
                                        
                                        <td class="text-center"><?php echo e($data->approval_status); ?></td>
                                        <td class="text-center">
                                            <a href='#modal-list-data'
                                               class='modal-list-data-action btn btn-xs btn-info'
                                               data-toggle='modal'
                                               data-url='/list/qt/detail/<?php echo e($data->supplier_id); ?>/<?php echo e($data->qt_no); ?>/<?php echo e($data->qt_bsv_attendance_id); ?>/'
                                               data-title='<?php echo e($data->name); ?>'>
                                                <i class="fa fa-info"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div id="qt-detail"></div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
    <script>
        var APP_URL = <?php echo json_encode(url('/')); ?>


        App.datatables();
        /* Initialize Datatables */
        var tableListData = $('#attend-list').DataTable({
            columnDefs: [{orderable: false, targets: [0]}],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        });

        $(document).ready(function () {

            $('.table-responsive').on("click", '.modal-list-data-action', function () {

                $('.spinner-loading').show();
                $('#qt-detail').html('').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data)
                        $('.spinner-loading').hide();
                        $('#qt-detail').html($data).fadeIn();
                    }
                });

            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_qt_qtno.blade.php ENDPATH**/ ?>