<?php $__env->startSection('content'); ?>
        <?php if($result != null && count($result) > 0): ?>
            <div class="block">
                <!-- Block Title -->
                <div class="block-title">
                    <h2><strong>Telnet Connection to Server 1GFMAs (Batch File)</strong></h2>
                </div>
                <!-- END Block Title -->
                
                <!-- Block Content -->
                <address>
                <?php $__currentLoopData = $result; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <strong> <?php echo e($data); ?><br/>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </address>
                <!-- END Block Content -->
               
            </div>
        <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\telnet.blade.php ENDPATH**/ ?>