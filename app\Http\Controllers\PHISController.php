<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\PhisService;
use Illuminate\Http\Request;

class PHISController extends Controller {

    use SupplierService;
    use PhisService;

use OSBService;

use ProfileService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function wsOSBLog(){
        return view('list_phis', [
            'listdata' => null,
            'listXml' => null,
            'carian' => '']);
    }
    public function searchWsOSBLog($search){
        $list = $this->getListOSBPHISWebServiceDetails($search);
        return view('list_phis', [
            'listdata' => $list,
            'carian' => $search]);
    }
    
    public function phisView() {
        return view('phis_details', [
            'result' => null
        ]);
    }
    
    public function PhisSearchOrderDetails(Request $request){
        $searchType = $request->searchType;
        $tabName = '';
        
        if ($searchType == 'search_docno'){
            $docNo = $request->doc_no;
            $phisNo = $request->phis_no;
            $tabName = 'search-tab-docno';
            
            if ($docNo) {
                $listDetailDoc = $this->getDocNoCRCO($docNo);
                $result = $listDetailDoc;
            }            
            
            if($phisNo) {
                $listDetailPhisNo = $this->getPhisNo($phisNo);
                $result = $listDetailPhisNo;
            }
        }
        
        if($result == null) {
            $result = 'notfound';
        }
        
        return view('phis_details', [
            
            'result' => $result,            
            'msg' => null,
            'tabName' => $tabName,
            'formSearch' => $request->all()
        ]);
        
    }  

}
