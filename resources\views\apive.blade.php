@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carianform" action="{{url('/find/gfmas')}}/{{$type}}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    @if($result != null && count($result) > 0)
   
        
            

       
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Senarai Apive Fail </strong><small>Hasil Carian : {{$carian}}</small></h1>
            <a href="{{url('/trigger/gfmas/apive')}}/?ep_no={{$carian}}"  target="_blank" 
               class="btn btn btn-default pull-right"  style="margin:5px;">Trigger APIVE</a>
        </div>
        
        
        @if($result && count($result) > 0 )
        @foreach($result as $obj)
        <div class="block collapse panel-xml" id="row_{{$obj["key"]}}">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> {{$obj["FileName"]}}</h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup">{{$obj["contents"]}}</code>
            </pre>
        </div>
        @endforeach
        @endif
            
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">FileName</th>
                        <th class="text-center">StatusSent</th>
                        <th class="text-center">Transaction Date</th>
                        <th class="text-center">Status Desc</th>
                        <th class="text-center">TransID</th>
                        <th class="text-center">Response 1GFMAS</th>
                        <th class="text-center">Details Link</th>
                    </tr>
                </thead>
                <tbody>
                @foreach($result as $obj)
                    <tr>
                        <td class="text-center text-success">
                            <a href="javascript:void(0)" data-toggle="collapse" data-target="#row_{{$obj["key"]}}" >{{$obj["FileName"]}}</a>
                            </td>
                        <td class="text-center">{{$obj["StatusSent"]}}</td>
                        <td class="text-center">@if($obj["InfoDetails"] != null){{$obj["InfoDetails"]->trans_date}}@endif</td>
                        <td class="text-center">@if($obj["InfoDetails"] != null){{$obj["InfoDetails"]->status_desc}}@endif</td>
                        <td class="text-center">@if($obj["InfoDetails"] != null){{$obj["InfoDetails"]->trans_id}}@endif</td>
                        @if($obj["ApoveFileName"] != null)
                        <td class="text-center text-success">{{$obj["StatusApove"]}}</td>
                        @endif
                        @if($obj["AperrFileName"] != null)
                        <td class="text-center text-danger"><strong>{{$obj["StatusApove"]}}</strong></td>
                        @endif
                        <td class="text-center">
                            @if($obj["ApoveFileName"] != null)
                            <a class="text-success" href="{{url('/find/osb/batch/file')}}/{{$obj["ApoveFileName"]}}" target="_blank">{{$obj["ApoveFileName"]}}</a>
                            @endif
                            <br />
                            @if($obj["AperrFileName"] != null)
                            <strong>
                            <a class="text-danger" href="{{url('/find/osb/batch/file')}}/{{$obj["AperrFileName"]}}" target="_blank" >{{$obj["AperrFileName"]}}</a>
                            </strong>
                            @endif
                        </td>
                            
                    </tr>
                @endforeach    
                </tbody>
            </table>
            <br />
        </div>

    </div>

                
    </div>
        
    @elseif($result != null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                    <small>Tidak dijumpai!</small>
                </h1>
            </div>
        </div>
    @else
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Carian Apive (MasterDataVendor) Fail<br>
                    <small>Masukkan eP No. pada carian diatas...</small>
                </h1>
            </div>
        </div>
    @endif
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endsection