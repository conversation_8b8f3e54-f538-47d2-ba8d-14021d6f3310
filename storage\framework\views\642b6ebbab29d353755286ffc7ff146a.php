<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/support/task/list')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...  (Case No, Company Name, Description)">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <?php if($listdata == null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <?php if($result == 'notfound'): ?>
                        <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                        <small>Rekod tidak dijumpai!</small>
                    <?php else: ?>
                        <i class="gi gi-search"></i>Carian Tugasan<br>
                        <small>Masukkan carian diatas...</small>
                    <?php endif; ?>
                </h1>
            </div>
        </div>
    <?php endif; ?>

    <?php if($listdata != null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Task for support eP<br>
                    <small>For Customer Service</small>
                </h1>
            </div>
        </div> 
        
        <?php if($success && $success == 'success'): ?>
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Success</h4> Task <a href="javascript:void(0)" class="alert-link">saved</a>!
        </div>
        <?php elseif($success && $success == 'failed'): ?>
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Failed</h4> Task <a href="javascript:void(0)" class="alert-link">failed</a>!
        </div>
        <?php endif; ?>


        <div class="block block-alt-noborder full">
            <span style="display:none" id="loadingTaskForm"> Repopulate Data. Please Wait... <i class="fa fa-spinner fa-spin"></i> </span>
            <div class="row" id="panelFormTask" 
                <?php if($errors->any()): ?> style="display:block" <?php else: ?> style="display:none" <?php endif; ?>>
                <div class="col-md-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong><span id="titleTask">Add Task</span></strong></h2>
                            <div class="block-options pull-right">
                                <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                                    onclick="$('#panelFormTask').hide();    $('#taskViewOther').hide();" >Close</span>
                            </div>
                        </div>

                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger">
                                <ul>
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        <form id="form-task" action="<?php echo e(url("/support/task")); ?>" method="post" class="form-horizontal form-bordered">
                            <?php echo e(csrf_field()); ?>

                            <input name="_method" id="_method"  type="hidden" value="POST">
                            <input type="hidden" name="task_id"  id="task_id" value="" />
                            <span id="categories" class="hide" ><?php echo e($categories); ?></span>
                            <div class="col-md-6">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="category_id">Category <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="category_id" name="category_id" class="form-control">
                                                    <option value="">Please select</option>
                                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($cat->category_id); ?>" <?php if(old('category_id') == $cat->category_id): ?> selected <?php endif; ?>><?php echo e($cat->category_name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="case_no">Case No. <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" id="case_no" name="case_no" class="form-control" placeholder="Case No CRM.."
                                                    value="<?php echo e(old('case_no')); ?>">
                                                <input type="hidden" name="case_type"  id="case_type" value="" />
                                                <span class="input-group-addon"><i class="gi gi-qrcode"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="entity_name">Company Name <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" id="entity_name" name="entity_name" class="form-control" placeholder="Company Name.."
                                                value="<?php echo e(old('entity_name')); ?>">
                                                <span class="input-group-addon"><i class="gi gi-nameplate"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="description">Description<span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <textarea id="description" name="description" rows="6" class="form-control" placeholder="Details Information.."><?php echo e(old('description')); ?></textarea>
                                        </div>
                                    </div>

                                    <div class="form-group form-group-resolution" style="display:none;">
                                        <label class="col-md-3 control-label" for="resolution">Resolution<span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <textarea id="resolution" name="resolution" rows="3" class="form-control" placeholder="Resolution Information.."></textarea>
                                        </div>
                                    </div>
                                    
                                </fieldset>
                            </div>
                            <div class="col-md-6 hide" id="taskViewOther">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="status_id">Status <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="status_id" name="status_id" class="form-control">
                                                    <option value="status_id">Please select</option>
                                                    <?php $__currentLoopData = App\Services\EPService::$TASK_STATUS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    
                                                    <option value="<?php echo e($key); ?>" <?php if(old('status_id') == $key): ?> selected <?php endif; ?>><?php echo e($value); ?></option>
                                                   
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-pin"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label">Created</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <span class="form-control" id="viewCreated"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label">Modified</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <span class="form-control" id="viewModified"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label">Completed</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <span class="form-control" id="viewCompleted"></span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                </fieldset>
                            </div>
                            <div class="form-group form-actions form-actions-button">
                                <div class="col-md-8 col-md-offset-4">
                                    <button type="submit" class="btn btn-sm btn-primary"><i class="fa fa-arrow-right"></i><span id="btn_save_span"> Save</span></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Customer Addresses Block -->
            <div class="block" id="panelListTask">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>List Tasks</strong>
                    </h1>
                    <div class="block-options pull-right action-today">
                        <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                           data-toggle="modal" data-url="<?php echo e(url("/support/report/log/task")); ?>/<?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?>" 
                           data-title="List Action Tasks by Today ">View Today Action</a>
                    </div>
                </div>
                <div class="col-md-12" >
                    <div class="block">
                        <form id="form-search-task" action="<?php echo e(url("/support/task/list")); ?>" method="post" class="form-horizontal form-bordered">
                            <?php echo e(csrf_field()); ?>

                            <input name="_method" id="_method"  type="hidden" value="POST">
                            <div class="col-md-6">
                                
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="search_category_id">Category <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="search_category_id" name="search_category_id" class="form-control">
                                                    <option value="">Please select</option>
                                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($cat->category_id); ?>" 
                                                            <?php if(isset($formSearch)): ?>
                                                                <?php if($cat->category_id == $formSearch["search_category_id"] ): ?> selected <?php endif; ?>
                                                            <?php endif; ?>><?php echo e($cat->category_name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-6">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="search_status_id">Status <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="search_status_id" name="search_status_id" class="form-control">
                                                    <option value="status_id">Please select</option>
                                                    <?php $__currentLoopData = App\Services\EPService::$TASK_STATUS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    
                                                    <option value="<?php echo e($key); ?>"
                                                            <?php if(isset($formSearch)): ?>
                                                                <?php if($key == $formSearch["search_status_id"] ): ?> selected <?php endif; ?>
                                                            <?php endif; ?>><?php echo e($value); ?></option>
                                                   
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-pin"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions form-actions-button text-right">
                                
                                    <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i>Search</button>
                                    <a href="<?php echo e(url('/support/task')); ?>" class="btn btn-sm btn-warning"><i class="fa fa-repeat"></i> Reset</a>
                               
                            </div>
                        </form>
                        
                    </div>
                </div>
                <button type="button" id="openAddTask" class="btn btn btn-info" >Add Task</button>
                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Category</th>
                            <th class="text-center">Company</th>
                            <th class="text-center">Description</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Case No</th>
                            <th class="text-center">Created</th>
                            <th class="text-center">Completed</th>
                            <th class="text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-center"><?php echo e($data->category_name); ?></td>
                                <td class="text-center"><?php echo e($data->entity_name); ?></td>
                                <td class="text-left" width="30%" >
                                <textarea rows="5" class="form-control" style="width: 100%" readonly><?php echo e($data->description); ?></textarea>
                                </td>
                                <td class="text-center">
                                    <?php if($data->status == 0): ?>
                                    <a href="javascript:void(0)" class="label label-warning"><?php echo e(App\Services\EPService::$TASK_STATUS[$data->status]); ?></a>
                                    <?php endif; ?>
                                    <?php if($data->status == 1): ?>
                                    <a href="javascript:void(0)" class="label label-success"><?php echo e(App\Services\EPService::$TASK_STATUS[$data->status]); ?></a>
                                    <?php endif; ?>
                                    <?php if($data->status == 2): ?>
                                    <a href="javascript:void(0)" class="label label-warning"><?php echo e(App\Services\EPService::$TASK_STATUS[$data->status]); ?></a>
                                    <?php endif; ?>
                                    <?php if($data->status == 3): ?>
                                    <a href="javascript:void(0)" class="label label-danger"><?php echo e(App\Services\EPService::$TASK_STATUS[$data->status]); ?></a>
                                    <?php endif; ?>
                                    
                                    
                               </td>
                               <td class="text-center"><?php echo e($data->case_no); ?>  <?php echo e($data->case_type); ?></td>
                                <td class="text-center"><?php echo e($data->created_at); ?> &nbsp; <?php echo e($data->created_by); ?></td>
                                <td class="text-center"><?php echo e($data->completed_at); ?> &nbsp; <?php echo e($data->completed_by); ?></td>
                                <td class="text-center action_table_task">
                                    <div class="btn-group btn-group-xs">
                                        
                                        <?php if($data->status == 1): ?>
                                        <a class="btn btn-primary action_table_view_task" data-toggle="tooltip" title="" data-original-title="Show Details"
                                            href="javascript:void(0)" data-toggle="modal" data-id="<?php echo e($data->task_id); ?>"><i class="hi hi-eye-open"></i></a>
                                        <?php else: ?>
                                        <a class="btn btn-primary action_table_edit_task" data-toggle="tooltip" title="" data-original-title="Edit Task"
                                            href="javascript:void(0)" data-toggle="modal" data-id="<?php echo e($data->task_id); ?>"><i class="fa fa-pencil"></i></a>
                                        <a class="btn btn-primary action_table_complete_task" data-toggle="tooltip" title="" data-original-title="Set to complete"
                                            href="javascript:void(0)" data-toggle="modal" data-id="<?php echo e($data->task_id); ?>"><i class="hi hi-ok"></i></a>
                                        <?php endif; ?>
                                        
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
        

        <?php echo $__env->make('_shared._modalListLogAction', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php endif; ?>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    function openFormTask(typeForm){
        $('#to-top').click(); //move scroll up to top page
        
        $('#panelFormTask').removeClass('hide'); 
        $('#panelFormTask').show(); 
            
        if(typeForm === 'add'){
            $('#taskViewOther').hide();
            $('.form-actions-button').show();
            $('.form-group-resolution').hide();
            $("#titleTask").text("Add Task");
            $("#btn_save_span").text("Save");
            $("#_method").val("POST");
        }else if(typeForm === 'edit'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $('.form-group-resolution').hide();
            $("#titleTask").text("Update Task");
            $("#btn_save_span").text("Save");
            $("#_method").val("POST");
        }else if(typeForm === 'complete'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $('.form-group-resolution').show();
            $("#titleTask").text("To Complete Task");
            $("#btn_save_span").text("Set Completed");
            $("#_method").val("PUT");
            
            $("#category_id").attr("readonly","readonly");
            $("#status_id").attr("readonly","readonly");
            $("#case_no").attr("readonly","readonly");
            $("#entity_name").attr("readonly","readonly");
            $("#description").attr("readonly","readonly");
            $("#resolution").removeAttr("readonly");
        }else if(typeForm === 'view'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $('.form-group-resolution').show();
            $("#titleTask").text("View Detail Task");
            $("#btn_save_span").parent().hide();
            $("#_method").val("GET");
            
            $("#category_id").attr("readonly","readonly");
            $("#status_id").attr("readonly","readonly");
            $("#case_no").attr("readonly","readonly");
            $("#entity_name").attr("readonly","readonly");
            $("#description").attr("readonly","readonly");
            $("#resolution").attr("readonly","readonly");
        }
        
        $("#category_id").val("");
        $("#status_id").val("");
        $("#task_id").val("");
        $("#case_no").val("");
        $("#description").text("");
        $("#entity_name").val("");
        $("#resolution").text("");
        
        
    }
    $( "#category_id" ).bind( "click change", function() {
        var categories = JSON.parse($("#categories" ).text());
        var catId = $(this).val();
        if(parseInt(catId) > 0){
            var cats = JSON.search( categories, '//*[category_id="'+catId+'"]' );
            var catDesc = cats[0].category_desc;
            //console.log(catDesc);
            $('#description').text(catDesc);
        }
    });
    
    $( "#case_no" ).bind( "change focusout", function() {
        var case_no = $(this).val();
        console.log('caseno: '+case_no);
        $('#case_type').val('');
        $('#entity_name').val('');
        $.ajax({
            url: "/crm/case/"+case_no,
            context: document.body
        }).done(function(resp) {
            var obj = JSON.parse(resp);
            if(obj !== null){
                if(obj.hasOwnProperty('account_name')){
                    $('#entity_name').val(obj.account_name);
                }
                if(obj.hasOwnProperty('request_type_c')){
                    $('#case_type').val(obj.request_type_c);
                }
                //$('#description').val(obj.description);
            }
        });
    });

    $('div#panelListTask').on("click",'#openAddTask', function(){
        openFormTask("add");
    });

    $('td.action_table_task').on("click",'a.action_table_edit_task', function(){
        
        var taskID = $(this).attr('data-id');
        console.log("Task ID: "+taskID);
        openFormTask("edit");
        
        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support/task/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);

            $("#task_id").val(obj.task_id);
            $("#category_id").val(obj.category_id);
            $("#case_no").val(obj.case_no);
            $("#entity_name").val(obj.entity_name);
            $("#description").text(obj.description);
            $("#resolution").text(obj.resolution);
            $("#status_id").val(obj.status);
            $("#viewCreated").text(obj.created_by+ ' on '+obj.created_at);
            if(obj.updated_by !== null){
                $("#viewModified").text(obj.updated_by+ ' on '+obj.updated_at);
            }
            if(obj.completed_by !== null){
                $("#viewCompleted").text(obj.completed_by+ ' on '+obj.completed_at);
            }
        });
    });

    $('td.action_table_task').on("click",'a.action_table_complete_task', function(){
        $('#to-top').click();
        var taskID = $(this).attr('data-id');
        console.log("Task ID: "+taskID);
        openFormTask("complete");
        

        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support/task/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);
            
            $("#task_id").val(obj.task_id);
            $("#category_id").val(obj.category_id);
            $("#case_no").val(obj.case_no);
            $("#entity_name").val(obj.entity_name);
            $("#description").text(obj.description);
            if(obj.resolution !== null){
                $("#resolution").text(obj.resolution);
            }
            $("#status_id").val(obj.status);
            $("#viewCreated").text(obj.created_by+ ' on '+obj.created_at);
            if(obj.updated_by !== null){
                $("#viewModified").text(obj.updated_by+ ' on '+obj.updated_at);
            }
            if(obj.completed_by !== null){
                $("#viewCompleted").text(obj.completed_by+ ' on '+obj.completed_at);
            }
        });
    });

    $('td.action_table_task').on("click",'a.action_table_view_task', function(){
        $('#to-top').click();
        var taskID = $(this).attr('data-id');
        console.log("Task ID: "+taskID);
        openFormTask("view");
        
        

        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support/task/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);

            $("#task_id").val(obj.task_id);
            $("#category_id").val(obj.category_id);
            $("#case_no").val(obj.case_no);
            $("#entity_name").val(obj.entity_name);
            $("#description").text(obj.description);
            $("#resolution").text(obj.resolution);
            $("#status_id").val(obj.status);
            $("#viewCreated").text(obj.created_by+ ' on '+obj.created_at);
            if(obj.updated_by !== null){
                $("#viewModified").text(obj.updated_by+ ' on '+obj.updated_at);
            }
            if(obj.completed_by !== null){
                $("#viewCompleted").text(obj.completed_by+ ' on '+obj.completed_at);
            }
        });
    });

</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_task.blade.php ENDPATH**/ ?>