<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/items/supplier')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Item Pembekal<br>
                <small>Masukkan Dokumen Number (RNC), MOF Number, EP No. , <PERSON><PERSON>, Nama Item, Identification No (ICNO)  pada carian diatas...
                <br />
                <strong>Senarai Item Pembekal yang perlu tindakan dari Codification Team.</strong>
                </small>
            </h1>
        </div>
    </div>

    <?php if($listdata == null || count($listdata) == 0): ?>
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <?php if(isset($error)): ?>
                  <p>Carian mesti lebih 3 aksara!</p>
                  <?php else: ?>
                  <p>Tidak dijumpai!</p>
                  <?php endif; ?>
              </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    
    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Item Pembekal</strong>
                        <small>Hasil Carian : <?php echo e($carian); ?></small>
                    </h1>
                    <div class="block-options pull-right action-today">
                        <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                           data-toggle="modal" data-url="<?php echo e(url('/support/report/log/item-codification')); ?>/<?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?>" 
                           data-title="List Action Today ">View Today Action</a>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">CHANGED DATE</th>
                            <th class="text-center">CHANGED BY</th>
                            <th class="text-center">REQUEST DATE</th>
                            <th class="text-center">DOC_NO</th>
                            <th class="text-center">DOC_TYPE</th>
                            <th class="text-center">PRODUCT_NAME</th>
                            <th class="text-center">STATUS</th>
                            <th class="text-center">COMPANY NAME</th>
                            <th class="text-center">EP NO.</th>
                            <th class="text-center">MOF NO.</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-left"><?php echo e($data->changed_date); ?></td>
                                <td class="text-left"><?php echo e($data->changed_by); ?></td>
                                <td class="text-left"><?php echo e($data->code_request_date); ?></td>
                                <td class="text-left"><?php echo e($data->doc_no); ?></td>
                                <td class="text-left"><?php echo e($data->doc_type); ?></td>
                                <td class="text-left"><?php echo e($data->product_name); ?></td>
                                <td class="text-left"><?php echo e($data->status_name); ?></td>
                                <td class="text-left"><?php echo e($data->company_name); ?></td>
                                <td class="text-left"><?php echo e($data->ep_no); ?></td>
                                <td class="text-left"><?php echo e($data->mof_no); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
       
    <?php endif; ?>

    <?php echo $__env->make('_shared._modalListLogAction', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    
</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_item_supplier.blade.php ENDPATH**/ ?>