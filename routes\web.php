<?php


use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;

// Route::get('/', function () {
//     return view('welcome');
// });

use App\Http\Controllers\HomeController;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\EpController;
use App\Http\Controllers\ActionEpController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\EpOrganizationController;
use App\Http\Controllers\EpSupportController;
use App\Http\Controllers\PaymentSupplierController;
use App\Http\Controllers\GFMASController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PHISController;
use App\Http\Controllers\QtController;
use App\Http\Controllers\OSBController;
use App\Http\Controllers\BPMController;
use App\Http\Controllers\AppSchedulerController;
use App\Http\Controllers\FulfilmentController;
use App\Http\Controllers\CrmSsm\CrmSsmController;


// For Laravel 12.x, Auth::routes() may require laravel/ui or breeze. Uncomment if using appropriate package.
// Auth::routes();

Route::get('/home', [HomeController::class, 'index'])->name('home');


Route::get('/', function () {
    return redirect(env('APP_EPSS_URL', 'https://epss.eperolehan.gov.my/login'));
    //return view('welcome');
});
Route::get('/login', function () {
    return redirect(env('APP_EPSS_URL', 'https://epss.eperolehan.gov.my/login'));
    //return view('welcome');
});

Route::get('/crm/casedetail/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];

    $fullPath = storage_path('app/exports/cases/'.$fileName);
    return response()->download($fullPath, $fileName, $headers); 
});

Route::get('/aspect/report/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];

    $fullPath = storage_path('app/exports/aspect/'.$fileName);
    return response()->download($fullPath, $fileName, $headers); 
});
Route::get('/crmssm/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];

    $fullPath = storage_path('app/exports/crmssm/'.$fileName);
    return response()->download($fullPath, $fileName, $headers); 
});

Route::get('/crmgamuda/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];

    $fullPath = storage_path('app/exports/crmgamuda/'.$fileName);
    return response()->download($fullPath, $fileName, $headers); 
});

Route::get('/crmjbal/download/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];

    $fullPath = storage_path('app/exports/crmssm/'.$fileName);
    return response()->download($fullPath, $fileName, $headers); 
});

Route::get('/case-stat-report', function () {
    $report = new \App\Report\Crm\CaseStatisticReport;
    $dateReport = \Carbon\Carbon::now();
    $report->runToSpecificPerson($dateReport);
    return "Successfully sent email"; 
});

Route::get('/case-stat-report-users', function () {
    $report = new \App\Report\Crm\CaseStatisticReport;
       $dateReport = \Carbon\Carbon::now();
       $report->runStatisticUsers($dateReport);
       return "Successfully sent email";
});


/** This for webservice eAduan. eAduan will call this api to sync data **/
Route::get('/rest/user/{loginID}', [BatchController::class, 'syncUser']);
Route::get('/rest/supplier/user/{loginID}', [BatchController::class, 'syncSupplierUser']);
Route::get('/rest/organization/user/{loginID}', [BatchController::class, 'syncOrganizationUser']);
Route::get('/rest/organization/orgcode/{orgcode}', [BatchController::class, 'syncOrganization']);
Route::get('/rest/organization/orgname/{orgname}', [BatchController::class, 'syncOrganizationByName']);


/** This for search eP **/

// Can access any roles
Route::get('/find/icno/', [EpController::class, 'detailsUser']);
Route::get('/find/icno/{icno}', [EpController::class, 'getDetailsUser']);
Route::get('/find/mofno/', [EpController::class, 'detailsUserByMof']);
Route::get('/find/mofno/{mofno}', [EpController::class, 'getDetailsUserByMof']);
Route::get('/find/epno/', [EpController::class, 'detailsUserByEpNo']);
Route::get('/find/epno/{epno}', [EpController::class, 'getDetailsUserByEpNo']);
Route::get('/find/sap/', [EpController::class, 'detailsUserBySapVendorCode']);
Route::get('/find/sap/{sapvendercode}', [EpController::class, 'getDetailsUserBySapVendorCode']);
Route::get('/find/osblog/{softcertreqid}/{servicecode}', [EpController::class, 'getSoftcertLogDetail']);
Route::get('/find/success-signing/{icno}/{type}', [EpController::class, 'getLatestSuccessSigning']);

Route::get('/find/userpersonnel/{applId}/{personnelId}', [ActionEpController::class, 'searchUserPersonnelDetails'])
	->middleware(['auth', 'roles']);
Route::post('/find/userpersonnel/{applId}/{personnelId}', [ActionEpController::class, 'updateUserPersonnelDetails'])
	->middleware(['auth', 'roles']);


Route::get('/find/uom/', [EpController::class, 'listUom']);
Route::get('/find/uom/{uom}', [EpController::class, 'getListUom']);

Route::get('/find/item/', [ItemController::class, 'searchListItems']);
Route::get('/find/item/{search}', [ItemController::class, 'searchListItems']);
Route::get('/find/items/unspsc', [ItemController::class, 'searchListUNSPSCItems']);
Route::get('/find/items/unspsc/{search}', [ItemController::class, 'searchListUNSPSCItems']);
Route::get('/find/item/unspsc/brand/{search}', [ItemController::class, 'searchListItemBrandByUNSPSCID']);
Route::get('/find/item/unspsc/type/{search}', [ItemController::class, 'searchListItemTypeByUNSPSCID']);
Route::get('/find/item/unspsc/measurement/{search}', [ItemController::class, 'searchListItemMeasurementByUNSPSCID']);
Route::get('/find/item/unspsc/color/', [ItemController::class, 'searchListItemColor']);
Route::get('/find/items/supplier', [ItemController::class, 'searchListProductSupplierPendingCodification']);
Route::get('/find/items/supplier/{search}', [ItemController::class, 'searchListProductSupplierPendingCodification']);

Route::match(['get', 'post','put'], '/find/items/codi-task', [ItemController::class, 'searchListProductSupplierCodificationTask']);
Route::match(['get', 'post','put'], '/find/items/codi-task/{search}', [ItemController::class, 'searchListProductSupplierCodificationTask']);



Route::get('/find/identity/{icNo}', [EpController::class, 'getIdentity']);

Route::get('/find/orgcode/{orgcode}', [EpOrganizationController::class, 'searchByOrgCode']);
Route::get('/find/orgcode/', [EpOrganizationController::class, 'searchByOrgCodeDefault']);
Route::get('/find/org/icno/{icno}', [EpOrganizationController::class, 'searchByIdentificationNo']);
Route::get('/find/org/icno/', [EpOrganizationController::class, 'searchByIdentificationNoDefault']);

Route::get('/download/mofcert/{mofCert}', function ($mofCert) {
    //dd($mofCert);
    //$remotePath =  "/docuRepo/prd/ngep/2018/SM/VIRTUAL_CERT_NEW/2018-02-23/374161_20180223_174440.pdf";
    $data = DB::connection('oracle_nextgen_rpt')->table('SM_MOF_CERT')->where('cert_serial_no',$mofCert)
            ->where('record_status',1)->first();
    if($data){
        if($data->file_path != null){
            $remotePath =  "/docuRepo/prd/ngep/".$data->file_path."/".$data->file_name;
            //dd($remotePath);
            $contents = SSH::into('portal')->getString($remotePath);
            return response()->attachmentPdf($contents);
        }
    }
    return "Supplier must login to eP and download first!";
});

Route::get('/download/attachment/cancel-reject/{attId}', function ($attId) {
    $data = DB::connection('oracle_nextgen_rpt')->table('sm_attachment')->where('attachment_id',$attId)->first();
    if($data){
        if($data->file_path != null){
            $remotePath =  "/docuRepo/prd/ngep/".$data->file_path."/".$data->file_name;
            $contents = SSH::into('portal')->getString($remotePath);
            return response()->attachmentPdf($contents);
        }
    }
    return "Document not found!";
});

//Route::get('/support/task', [EpSupportController::class, 'listEpTask']);
Route::match(['get', 'post','put'], '/support/task', [EpSupportController::class, 'listEpTask']);
Route::get('/support/task/save', [EpSupportController::class, 'saveTask']);
Route::get('/support/task/detail/{taskID}', [EpSupportController::class, 'getTask']);
Route::get('/support/task/list/{carian}', [EpSupportController::class, 'searchEpTask']);
Route::post('/support/task/list', [EpSupportController::class, 'searchEpTask2']);
Route::get('/crm/case/{caseno}', [EpSupportController::class, 'getDetailCaseCRM']);


Route::match(['get', 'post','put'], '/support/task-missing', [EpSupportController::class, 'listEpTaskMissing']);
Route::get('/support/task-missing/detail/{taskID}', [EpSupportController::class, 'getTaskMissing']);
Route::get('/support/task-missing/list/{carian}', [EpSupportController::class, 'searchInputEpTaskMissing']);
Route::post('/support/task-missing/list', [EpSupportController::class, 'searchEpTaskMissing']);
Route::get('/support/task-missing/check/{caseno}', [EpSupportController::class, 'getCRMCaseAndCheckTaskMissing']);
Route::get('/support/task-missing/download', [EpSupportController::class, 'downloadTaskMissing']);
Route::get('/support/task-missing/upload', [EpSupportController::class, 'uploadReport']);
Route::match(['get', 'post'], '/support/task-missing/upload', [EpSupportController::class, 'uploadReport']);


//For MOLPAY checking pending payment
Route::get('/support/molpay/payment', [PaymentSupplierController::class, 'listPendingPaymentSupplier']);
Route::post('/support/molpay/payment/{orderID}', [PaymentSupplierController::class, 'removePendingPaymentSupplier']);
Route::get('/report/payment', [PaymentSupplierController::class, 'showPaymentStat'])
    ->middleware(['auth', 'roles']);

//For eP Support Statistic Action
Route::get('/support/report/log/{type}/{date}', [EpSupportController::class, 'getListActionLogHTML']);

//For eP Support Statistic Action 
Route::get('/support/report/log-detail/{type}/{search}', [EpSupportController::class, 'getListDetailInfoLogHTML']);


/*
 * No Use anymore

Route::get('/find/gfmas/prcr/', [GFMASController::class, 'logGfmasDetailsPrCr'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));
Route::get('/find/gfmas/prcr/{docNo}', [GFMASController::class, 'getLogGfmasDetailsPrCr'])
	->middleware(['auth', 'roles']);

Route::get('/find/gfmas/poco/', [GFMASController::class, 'logGfmasDetailsPoCo'])
	->middleware(['auth', 'roles']);
Route::get('/find/gfmas/poco/{docNo}', [GFMASController::class, 'getLogGfmasDetailsPoCo'])
	->middleware(['auth', 'roles']);
Route::get('/find/gfmas/mminf/', [GFMASController::class, 'logGfmasDetailsMMINF'])
	->middleware(['auth', 'roles']);
Route::get('/find/gfmas/mminf/{itemName}', [GFMASController::class, 'getLogGfmasDetailsMMINF'])
	->middleware(['auth', 'roles']);

*/

Route::get('/find/gfmas/apive/{epNo}', [GFMASController::class, 'getApiveDetails'])
	->middleware(['auth', 'roles']);
Route::get('/check/gfmas/apive/connection', [GFMASController::class, 'checkConnectionGFMAS'])
	->middleware(['auth', 'roles']);
Route::get('/dashboard/gfmas/', [DashboardController::class, 'getDashboardGfmas'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/quartz/', [DashboardController::class, 'getDashboardQuartz'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/apive/diinterfacelog', [DashboardController::class, 'getDashboardDiInterfaceLogApive'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/checkConnection', [DashboardController::class, 'checkConnectionGFMAS'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/osbretry', [DashboardController::class, 'checkOsbRetry'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/osbnotifyretry', [DashboardController::class, 'checkOsbNotifyRetry'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/osbbatchretry', [DashboardController::class, 'checkOsbBatchRetry'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/checkQtMonitoring', [DashboardController::class, 'checkQtMonitoring'])
    ->middleware(['auth', 'roles']);


/*
 * Stop first access this URL, cause slow query! Impact performace DB Report!
Route::get('/dashboard/apive/outbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboardApiveOutbound',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/apove/inbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboardApoveInbound',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkejbosb', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkEjbOsb',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkFileErrorInbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkFileErrorInbound',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkwsvalidation', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkWsValidationException',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkWsItemCodeErrorInGFM100', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkWsItemCodeErrorInGFM100',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkWsItemCodeErrorInMMINF', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkWsItemCodeErrorInMMINF',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
*/



Route::get('/dashboard/1gfmas/outbound', [DashboardController::class, 'getDashboard1GfmasOutbound'])
    ->middleware(['auth', 'roles']);
Route::get('/dashboard/1gfmas/inbound', [DashboardController::class, 'getDashboard1GfmasInbound'])
    ->middleware(['auth', 'roles']);

Route::get('/list/1gfmas/folder', [GFMASController::class, 'fetchFromFolderDisplay'])
    ->middleware(['auth', 'roles']);
Route::get('/list/1gfmas/fetch', [GFMASController::class, 'fetchFromFolderList'])
    ->middleware(['auth', 'roles']);
Route::post('/fetch/1gfmas/1gfmas-out', [GFMASController::class, 'fetch1GfmasOutFolder'])
    ->middleware(['auth', 'roles']);

Route::get('/find/1gfmas/ws/log/{name}', [GFMASController::class, 'listLogOSB'])
	->middleware(['auth', 'roles']);

Route::get('/find/1gfmas/ws/{search}', [GFMASController::class, 'searchWsOSBLog'])
	->middleware(['auth', 'roles']);
Route::get('/find/1gfmas/ws', [GFMASController::class, 'wsOSBLog'])
	->middleware(['auth', 'roles']);

/** MMINF TRIGGER **/
Route::get('/trigger/gfmas/mminf/', [GFMASController::class, 'mminfTriggerView'])
    ->middleware(['auth', 'roles']);
Route::post('/trigger/gfmas/mminf/search/', [GFMASController::class, 'searchPreMminf'])
    ->middleware(['auth', 'roles']);
Route::post('/trigger/gfmas/mminf/update/', [GFMASController::class, 'mminfTrigger'])
    ->middleware(['auth', 'roles']);
Route::post('/trigger/gfmas/mminf/docno/update', [GFMASController::class, 'mminfTriggerByDocNo'])
    ->middleware(['auth', 'roles']);

Route::get('/trigger/gfmas/mminf/quartz', [GFMASController::class, 'displayDashboardMminfQuartz'])
    ->middleware(['auth', 'roles']);
Route::get('/trigger/gfmas/mminf/diinterfacelog', [GFMASController::class, 'displayDashboardMminfDiInterface'])
    ->middleware(['auth', 'roles']);

/** APIVE TRIGGER **/
Route::get('/trigger/gfmas/apive/', [GFMASController::class, 'apiveTriggerView'])
    ->middleware(['auth', 'roles']);
Route::post('/trigger/gfmas/apive/search/', [GFMASController::class, 'searchPreApive'])
    ->middleware(['auth', 'roles']);
Route::post('/trigger/gfmas/apive/update/', [GFMASController::class, 'apiveTrigger'])
    ->middleware(['auth', 'roles']);


Route::get('/list/1gfmas/pending/batch/{type}', [GFMASController::class, 'listPendingBatchHTML'])
    ->middleware(['auth', 'roles']);

Route::get('/list/1gfmas/batch/{type}/{serviceCode}/{transDate}', [GFMASController::class, 'listBatchHTML'])
    ->middleware(['auth', 'roles']);


/* PHIS */
Route::get('/find/phis/ws/{search}', [PHISController::class, 'searchWsOSBLog'])
    ->middleware(['auth', 'roles']);
Route::get('/find/phis/ws', [PHISController::class, 'wsOSBLog'])
    ->middleware(['auth', 'roles']);

Route::get('/find/phis/view/', [PHISController::class, 'phisView'])
    ->middleware(['auth', 'roles']);
Route::post('/find/phis/search/', [PHISController::class, 'PhisSearchOrderDetails'])
    ->middleware(['auth', 'roles']);

/* QT */
Route::get('/find/qt/qtno', [QtController::class, 'searchPage'])
    ->middleware(['auth', 'roles']);
Route::get('/find/qt/qtno/{qtno}', [QtController::class, 'getSupplierAttendanceByQtNo'])
    ->middleware(['auth', 'roles']);
Route::get('/list/qt/detail/{supplierid}/{qtno}/{bsvattendanceid}', [QtController::class, 'getQtDetails'])
    ->middleware(['auth', 'roles']);
Route::get('/find/qt/proposal', [QtController::class, 'searchProposalSupplierByQuotationTender'])
    ->middleware(['auth', 'roles']);

Route::get('/find/qt/proposal/{qtno}', [QtController::class, 'searchProposalSupplierByQuotationTender'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));

Route::get('/find/qt/committee', [QtController::class, 'searchQTCommitteMembersByQuotationTender'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));

Route::get('/find/qt/committee/{qtno}', [QtController::class, 'searchQTCommitteMembersByQuotationTender'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));

Route::get('/find/qt/lawatan', [QtController::class, 'searchTaklimatOrLawatanTapakByQuotationTender'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));

Route::get('/find/qt/lawatan/{qtno}', [QtController::class, 'searchTaklimatOrLawatanTapakByQuotationTender'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));


/** OSB **/

Route::get('/find/osb/batch/file/{fileName}', [OSBController::class, 'searchBatchOSBLog'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));

Route::get('/find/osb/batch/file', [OSBController::class, 'batchOSBLog'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));

Route::get('/find/osb/log', [OSBController::class, 'OSBLog'])
    ->middleware(['auth', 'roles'])
    ->defaults('roles', Config::get('constant.roles_adv_ep'));

Route::get('/find/osb/log/{carian}', [OSBController::class, 'searchOSBLog'])
    ->middleware(['auth', 'roles']);
Route::get('/find/osb/decrypt/{filename}', [OSBController::class, 'decryptFile'])
    ->middleware(['auth', 'roles']);
Route::match(['get', 'post','put'], '/find/osb/error', [OSBController::class, 'searchListErrorTransactionOSB'])
    ->middleware(['auth', 'roles']);

Route::match(['get', 'post', 'put'], '/support/task', [EpSupportController::class, 'listEpTask']);


/** BPM **/
Route::get('/find/bpm/task/docno', [BPMController::class, 'getListTaskBpm'])
    ->middleware(['auth', 'roles']);
Route::get('/find/bpm/task/docno/{docno}', [BPMController::class, 'getListTaskBpmByDocNo'])
    ->middleware(['auth', 'roles']);

/** Application Scheduler **/
Route::get('/find/app-scheduler/SIT', [AppSchedulerController::class, 'getSIT'])
    ->middleware(['auth', 'roles']);

Route::get('/find/app-scheduler/Prod', [AppSchedulerController::class, 'getProd'])
    ->middleware(['auth', 'roles']);

/** FL **/
Route::get('/find/trans/docno/{docNo}', [FulfilmentController::class, 'searchTransactionDocNo']);
Route::get('/find/trans/track/docno/{docNo}', [FulfilmentController::class, 'getListDocNoTracking']);
Route::get('/find/trans/track/docno', [FulfilmentController::class, 'getListDocNoTrackingDefault']);


/*
 * CRM 
 */
Route::get('/rest/crm/{caseNumber}', [CrmSsmController::class, 'updateCase']);