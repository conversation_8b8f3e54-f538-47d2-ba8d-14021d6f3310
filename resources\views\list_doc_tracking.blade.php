@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/trans/track/docno/')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Tracking Diari<br>
                <small>Carian Dokumen No. &raquo;  Simple Quote (SQ),Quatation Tender (QT),  Request Note (RN) , LOA Number (LA), Contract No (CT), 
                    Purchase Request (PR), Contract Request (CR), Purchase Order (PO) and Contract Order (CO) only.</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Tracking Diari </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>
                
                @if(isset($supplier) && $supplier != null)
                <address>
                    <strong>Supplier Name </strong> : {{ $supplier->company_name }}<br />
                    <strong>Business Type </strong> : {{ $supplier->business_type }}  &raquo;  ({{ App\Services\EPService::$BUSINESS_TYPE[$supplier->business_type] }})<br />
                    <strong>SSM No </strong> : {{ $supplier->reg_no }}<br />
                    <strong>eP No </strong> : {{ $supplier->ep_no }}  <a target="_blank" href="{{ url('/find/gfmas/apive/') }}/{{ $supplier->ep_no }}" >Check Apive</a><br />
                    <strong>MOF No </strong> : {{ $supplier->mof_no }}  <a target="_blank" href="{{ url('/find/mofno') }}/{{ $supplier->mof_no }}" >Details Supplier</a><br />
                </address>                
                @endif
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">DOC NO.</th>
                            <th class="text-center">ACTION DESCRIPTION</th>
                            <th class="text-center">ACTION DATE</th>
                            <th class="text-center">ROLE</th>
                            <th class="text-center">STATUS</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-center">{{ $data->doc_no }}</td>
                                <td class="text-left">{{ $data->action_desc }}</td>
                                <td class="text-center">{{ $data->actioned_date }}</td>
                                <td class="text-center">{{ $data->role_code }}</td>
                                <td class="text-center">{{ $data->status_name }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endsection



