@extends('layouts.guest-dash')



@section('content')

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Transaction Error Logs  OSB <br>
            <small> </small>
        </h1>
    </div>

</div>

<div class="block">
    <div class="block-title">
        <h2><strong><PERSON>ian</strong></h2>
    </div>

    <div class="block">
        
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <form id="form-search-mminf" action="{{url("/find/osb/error")}}" method="post" class="form-horizontal" onsubmit="return true;">
            {{ csrf_field() }}
            <input name="_method" id="_method"  type="hidden" value="POST">
            <div class="form-group">
                <label class="col-md-3 control-label" for="service_code">Service Code</label>
                <div class="col-md-5">
                    <select id="service_code" name="service_code" class="select-select2"  data-placeholder="Choose a Service Code.." style="width: 100%;">
                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                       @foreach($listServiceCode as $serviceCodeObj)
                        <option value="{{$serviceCodeObj->service_code}}"  data-val ="{{old('service_code')}}"
                                @if(old('service_code') == $serviceCodeObj->service_code) selected @endif >{{$serviceCodeObj->service_code}} &raquo; {{$serviceCodeObj->service_name}}</option>
                       @endforeach 
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label" for="date_start">Tarikh </label>
                
                <div class="col-md-5">
                    <input type="text" id="date_start" name="date_start" class="form-control input-datepicker-close" 
                            data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                            value="@if(old('date_start') != null){{old('date_start')}}@else{{Carbon\Carbon::now()->format('Y-m-d')}}@endif">
                </div>
            </div>
           
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
        </form>
 
    </div>
</div>
@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Transaction Logs</strong></h1>
        </div>
        
        @if($listdata && count($listdata) > 0 )
        @foreach($listdata as $xml)
        <div class="block collapse panel-xml" id="{{$xml->trans_type}}_{{$xml->service_code }}_{{$xml->trans_id}}">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> {{$xml->trans_type}} | ({{$xml->service_code}}) | {{$xml->trans_id}}</h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup">{{$xml->payload_body}}</code>
            </pre>
        </div>
        @endforeach
        @endif
        <ul class="text-info">
            <li>IBReq : Refering to request</li>
            <li>IBRes : Refering to response  </li>
            <li>Click link in column service code to view each payload for transaction type.  </li>
        </ul>
        <div class="table-responsive">
            <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">TRANS ID</th>
                        <th class="text-center">TRANS TYPE</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">TRANS DATE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">STATUS CODE</th>
                        <th class="text-center">STATUS_DESC</th>
                        <th class="text-center">REMARKS 1</th>
                        <th class="text-center">REMARKS 2</th>
                        <th class="text-center">REMARKS 3</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center">{{$data->trans_id }}</td>
                        <td class="text-center">{{ $data->trans_type }}</td>
                        <td class="text-center">
                        <a href="javascript:void(0)" data-toggle="collapse" data-target="#{{ $data->trans_type }}_{{$data->service_code }}_{{$data->trans_id}}" >
                            {{$data->service_code }}</a></td>
                        <td class="text-center">{{ $data->trans_date }}</td>
                        <td class="text-left">{{ App\Services\EPService::$OSB_STATUS[$data->status] }}</td>
                        <td class="text-left">{{ $data->status_code }}</td>
                        <td class="text-left">@include('_shared._infoDetailErrorDescription')</td>
                        <td class="text-center"><a href="{{url('/find/1gfmas/ws')}}/{{ $data->remarks_1 }}" target="_blank"> {{ $data->remarks_1 }} </a></td>
                        <td class="text-center"><a href="{{url('/find/1gfmas/ws')}}/{{ $data->remarks_2 }}" target="_blank"> {{ $data->remarks_2 }} </a></td>
                        <td class="text-center"><a href="{{url('/find/1gfmas/ws')}}/{{ $data->remarks_3 }}" target="_blank"> {{ $data->remarks_3 }} </a></td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>
@endif
@include('_shared._modalDetailInfo')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>

@endsection
