<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;

class ReportMonitoringController extends Controller {

    public function __construct() {
        $this->middleware('auth');
    }

    public static function saveErrorSendReport($reportName, $reportDate, $reportCommand, $reportError) {
        DB::connection('mysql_ep_support')
                ->insert('insert into crm_report_monitoring  
                    (report_name,report_date,report_error,report_command,report_status,last_updated) 
                    values (?, ?, ?, ?, ?, ?)', [
                    $reportName,
                    $reportDate,
                    $reportError,
                    $reportCommand,
                    'Pending',
                    Carbon::now()
        ]);
    }
}
