/*
 *  Document   : modalListActionLogDatatable.js
 *  Author     : shamsul
 *  Description: Custom javascript code used in Tables Datatables page
 */

var ModalListActionLogDatatable = function() {

    return {
        init: function() {

            App.datatables();
            /* Initialize Datatables */
            var tableListData =     $('#action-log-datatable').DataTable({
                    columnDefs: [ { orderable: true, targets: [ 0 ] } ],
                    pageLength: 10,
                    lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });

            $('.action-today').on("click",'.modal-list-data-action', function(){

                $('.spinner-loading').show();
                $('#action-log-datatable').html('').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));
                
                $('#url_search_action_log').val($(this).attr('data-url'));

                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();

                $.ajax({
                    url: $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#action-log-datatable').html($data).fadeIn();
                        $('.spinner-loading').hide();
                        /* Re-Initialize Datatable */
                        tableListData = $('#action-log-datatable').DataTable({
                            columnDefs: [ { orderable: true, targets: [ 0 ] } ],
                            pageLength: 10,
                            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                        });


                    }
                });

            });
            
            $('#url_search_button_search').on("click", function(){

                $('.spinner-loading').show();
                $('#action-log-datatable').html('').fadeIn();

                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();
                
                var urlStr = $('#url_search_action_log').val();
                var cut_url = urlStr.substr(-10);
                var newUrl = urlStr.replace(cut_url, "");
                
                var sendUrl = newUrl + $('#date_action_log').val();
    
                $.ajax({
                    url: sendUrl,
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#action-log-datatable').html($data).fadeIn();
                        $('.spinner-loading').hide();
                        /* Re-Initialize Datatable */
                        tableListData = $('#action-log-datatable').DataTable({
                            columnDefs: [ { orderable: true, targets: [ 0 ] } ],
                            pageLength: 10,
                            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                        });


                    }
                });

            });
        }
    };
}();