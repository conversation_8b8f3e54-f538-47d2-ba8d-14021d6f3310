<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/qt/proposal/')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Semakan Tawaran Sebut Harga <br>
                <small>Semakan serahan pembekal untuk sebut harga.</small>
            </h1>
        </div>
    </div>

    <?php if($listdata == null || count($listdata) == 0): ?>
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian Semakan Tawaran Sebut Harga </strong>
                        <small>Hasil Carian : <?php echo e($carian); ?></small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">QT NO.</th>
                            <th class="text-center">PROPOSAL NO</th>
                            <th class="text-center">MOF NO.</th>
                            <th class="text-center">SUPPLIER</th>
                            <th class="text-center">SUBMITTED</th>
                            <th class="text-center">PROPOSAL SUBMITTION DATE</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-center"><?php echo e($data->qt_no); ?></td>
                                <td class="text-center"><?php echo e($data->proposal_no); ?></td>
                                <td class="text-center content-link">
                                    <a class="modal-list-data-action"
                                       href="<?php echo e(url('/find/mofno')); ?>/<?php echo e($data->mof_no); ?>" 
                                       target='_blank' >
                                       <?php echo e($data->mof_no); ?> </a>
                                <td class="text-left"><?php echo e($data->supplier_name); ?></td>
                                <td class="text-left"><?php echo e($data->submitted); ?></td>
                                <td class="text-left"><?php echo e($data->proposal_submit_date); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    <?php endif; ?>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_qt_proposal.blade.php ENDPATH**/ ?>