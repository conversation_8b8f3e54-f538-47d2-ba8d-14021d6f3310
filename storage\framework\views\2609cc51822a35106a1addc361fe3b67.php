<?php $__env->startSection('header'); ?>
    <!-- Search Form -->
    <form id="carianform" <?php if($type == 'orgcode'): ?> action="<?php echo e(url('/find')); ?>/<?php echo e($type); ?>/" <?php else: ?> action="<?php echo e(url('/find/org')); ?>/<?php echo e($type); ?>/" <?php endif; ?> method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...">
        </div>
    </form>
    <!-- END Search Form -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if($orginfo == null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <?php if($result == 'notfound'): ?>
                        <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                        <small>Rekod tidak dijumpai!</small>
                    <?php else: ?>
                        <?php if($type == 'orgcode'): ?>
                            <i class="gi gi-search"></i>Carian Organisasi Melalui Org Code<br>
                            <small>Masukkan Org Code pada carian diatas...</small>
                        <?php else: ?>
                            <i class="gi gi-search"></i>Carian Organisasi Melalui IC No.<br>
                            <small>Masukkan IC No. pada carian diatas...</small>
                        <?php endif; ?>
                    <?php endif; ?>
                </h1>
            </div>
        </div>
    <?php endif; ?>
    <?php if($orginfo): ?>
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title  panel-heading" style = "background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Organisasi : <?php echo e($orginfo[0]->org_name); ?></strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="block">
                            <div class="block-title">
                                <h2>Maklumat Organisasi</h2>
                            </div>
                            <?php if($orginfo[0]->hierarchy): ?>
                            <ul class="breadcrumb breadcrumb-top">
                                
                                <?php $__currentLoopData = $orginfo[0]->hierarchy; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hierarchy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><a href="<?php echo e(url('/find/orgcode')); ?>/<?php echo e($hierarchy['org_code']); ?>" 
                                           title="<?php echo e($hierarchy['org_type']); ?>" ><?php echo e($hierarchy['org_name']); ?></a></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                
                                
                            </ul>
                            <?php endif; ?>
                            
                            <h6><strong><?php echo e($orginfo[0]->org_name); ?></strong></h6>
                            <address>
                                <strong>Organization Profile ID </strong> : <?php echo e($orginfo[0]->org_profile_id); ?><br />
                                <strong>Organization Code </strong> : <?php echo e($orginfo[0]->org_code); ?><br />
                                <strong>Organization Type </strong> : <?php echo e($orginfo[0]->code_desc); ?><br />
                                <strong>Effective Date </strong> : <?php echo e($orginfo[0]->eff_date); ?><br />
                                
                                <strong>Changed Date </strong> : <?php echo e($orginfo[0]->changed_date); ?><br />
                            </address>
                        </div>
                    </div>
                </div>
                <!-- START USER DETAILS -->
                <?php if($listdata): ?>
                <div class="widget">
                    <div class="widget-extra themed-background-dark">
                        <h5 class='widget-content-light'><i class="fa fa-users"></i> <strong>Jumlah Pengguna : <?php echo e(count($listdata)); ?></strong></h5>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">

                            <div class="table-responsive">
                                <table id="basic-datatable" class="table table-striped table-vcenter">
                                    <thead>
                                    <tr>
                                        <th class="text-center">No.</th>
                                        <th class="text-center">Name</th>
                                        <th class="text-center">Identification No.</th>
                                        <th class="text-center">Role Admin</th>
                                        <th class="text-center">Email</th>
                                        <th class="text-center"></th>
                                    </tr>
                                    </thead>

                                    <tbody>
                                    <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                                            <td class="text-center"><?php echo e($data->fullname); ?></td>
                                            <td class="text-center"><?php echo e($data->identification_no); ?></td>
                                            <td class="text-center"><?php echo e($data->role_admin); ?></td>
                                            <td class="text-center"><?php echo e($data->email); ?></td>
                                            <td class="text-center">
                                                <button class="btn btn-info btn-xs"
                                                        data-toggle="collapse"
                                                        data-target="#row_<?php echo e($data->user_id); ?>">
                                                    Details
                                                </button>
                                            </td>
                                        </tr>
                                        <tr id="row_<?php echo e($data->user_id); ?>"  class="collapse">
                                            <td class="text-center" colspan="7">
                                                <div class="block"style="background-color: inherit;">
                                                    <div class="row">
                                                        <div class="col-sm-4">
                                                            <div class="block">
                                                                <div class="block-title">
                                                                    <h2>Maklumat User Login  </h2>
                                                                </div>
                                                                <address class="text-left">
                                                                    <strong>User ID </strong> : <?php echo e($data->user_id); ?><br />
                                                                    <strong>Login ID </strong> : <?php echo e($data->login_id); ?><br />
                                                                    <strong>Email </strong> : <?php echo e($data->email); ?><br />
                                                                    <strong>ICNO </strong> : <?php echo e($data->identification_no); ?><br />
                                                                    <strong>Mobile </strong> : <?php echo e($data->mobile_country); ?><?php echo e($data->mobile_area); ?><?php echo e($data->mobile_no); ?><br />
                                                                    <strong>Last Login Date </strong> : <?php echo e($data->login_date); ?><br />
                                                                    <strong>Last Date Changed </strong> :  <?php echo e($data->changed_date); ?> <br />
                                                                </address>
                                                            </div>
                                                        </div>
                                                        <?php if(count($data->listUserRole ) > 0): ?>
                                                            <div class="col-sm-4">
                                                                <div class="block">
                                                                    <div class="block-title">
                                                                        <h2>Peranan </h2>
                                                                    </div>
                                                                    <address class="text-left">
                                                                        <?php $__currentLoopData = $data->listUserRole; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <strong><?php echo e(++$indexKey); ?>) </strong><?php echo e($role->role_name); ?> <br/>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </address>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                        <?php if(count($data->listApprover ) > 0): ?>
                                                            <div class="col-sm-4">
                                                                <div class="block">
                                                                    <div class="block-title">
                                                                        <h2>Pelulus </h2>
                                                                    </div>
                                                                    <address class="text-left">
                                                                        <?php $__currentLoopData = $data->listApprover; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $approver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <strong><?php echo e(++$indexKey); ?>) </strong><?php echo e($approver->group_name); ?> (<?php echo e($approver->group_code); ?>)<br/>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </address>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function(){ TablesDatatables.init(); });</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\user_details_orgcode.blade.php ENDPATH**/ ?>