<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/osb/batch/file')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian Batch File<br>
            <small>Search file name only. </small>
        </h1>
    </div>
</div>


    <!-- Log Block -->
    <?php if($objFile != null): ?> 
    
        <div class="block">
            <!-- Log Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong><?php echo e($carian); ?></strong>  Details Information</h2>
            </div>
            <!-- END Log Title -->

            <!-- Log Content -->
            
            <div class="table-responsive">
                <table class="table table-bordered table-vcenter">
                    <tbody>
                        <tr>
                            <td width="350">Service Code</td>
                            <td class="text-info"><?php echo e($objFile->service_code); ?></td>
                        </tr>
                        <tr>
                            <td>Created Date</td>
                            <td class="text-info"><?php echo e($objFile->created_date); ?></td>
                        </tr>
                        <tr>
                            <td>Content File</td>
                            <td class="text-success content-file">
                                <strong>
                                    <a href="javascript:void(0)" data-toggle="collapse" data-target="#<?php echo e($objFile->batch_file_id); ?>" >
                                        Show</a>
                                </strong>
                                &nbsp;&nbsp;&nbsp;
                                <strong>
                                    <a href='#modal-file-decrypt'
                                        id ="link_decrypt_file"
                                        class='modal-list-data-action'
                                        data-toggle='modal'
                                        data-url='<?php echo e(url('/find/osb/decrypt')); ?>/<?php echo e($objFile->file_name); ?>'
                                        data-title='Decrypt Content File'>
                                         <strong style="color:forestgreen;">Show Decrypt</strong><br />
                                     </a>
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>Checking file successfully success and updated in eP (DI_INTERFACE_LOG)</td>
                            <?php if(isset($objFileCompletedProcess) && $objFileCompletedProcess != null): ?>
                            <td class="text-info">Process Completed at <?php echo e($objFileCompletedProcess->changed_date); ?></td>
                            <?php else: ?>
                            <td class="text-danger"><strong>No record found! Failed process and updated in eP</strong></td>
                            <?php endif; ?>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            
            
            
            
        
                
            <!-- END Log Content -->
            <div class="block collapse panel-xml" id="<?php echo e($objFile->batch_file_id); ?>">
                <div class="block-title">
                    <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> <?php echo e($objFile->file_name); ?></h2>
                    <div class="block-options pull-right">
                        <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                            onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                    </div>
                </div>
                <pre class="line-numbers">
                    <code class="language-markup"><?php echo e($objFile->file_data); ?></code>
                </pre>
            </div>
            
            <!-- MODAL: DECRYPT -->
            <div id="modal-file-decrypt" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header"> Title</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                                    </div>
                                    <pre class="line-numbers" style = "word-break: unset;">
                                        <code class="text-left language-css" style = "white-space: pre-wrap;display:block;"><div class="osb-detail"></div></code>
                                    </pre>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END Log Block -->
    
   <?php endif; ?> 

<?php if($listdata == null || count($listdata) == 0): ?>
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              <p>Carian Log OSB tidak dijumpai!</p>
          </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if($listdata && count($listdata) > 0): ?>

<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Logs Transaction : <?php echo e($carian); ?></strong></h1>
        </div>
        
        <?php if($listdata && count($listdata) > 0 ): ?>
        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $xml): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="block collapse panel-xml" id="<?php echo e($xml->trans_type); ?>_<?php echo e($xml->service_code); ?>_<?php echo e($xml->trans_id); ?>">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> <?php echo e($xml->trans_type); ?> | (<?php echo e($xml->service_code); ?>) | <?php echo e($xml->trans_id); ?></h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup"><?php echo e($xml->payload_body); ?></code>
            </pre>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    
        <div class="table-responsive">
            <table id="ws-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">TRANS ID</th>
                        <th class="text-center">TRANS TYPE</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">TRANS DATE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">REMARKS 1</th>
                        <th class="text-center">REMARKS 2</th>
                        <th class="text-center">REMARKS 3</th>
                    </tr>
                </thead>
                <tbody>
                <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="text-center"><?php echo e($data->trans_id); ?></td>
                        <td class="text-center"><?php echo e($data->trans_type); ?></td>
                        <td class="text-center">
                        <a href="javascript:void(0)" data-toggle="collapse" data-target="#<?php echo e($data->trans_type); ?>_<?php echo e($data->service_code); ?>_<?php echo e($data->trans_id); ?>" >
                            <?php echo e($data->service_code); ?></a></td>
                        <td class="text-center"><?php echo e($data->trans_date); ?></td>
                        <td class="text-left"><?php echo e($data->status_desc); ?></td>
                        <td class="text-center"><?php echo e($data->remarks_1); ?></td>
                        <td class="text-center"><?php echo e($data->remarks_2); ?></td>
                        <td class="text-center"><?php echo e($data->remarks_3); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
     
    $(document).ready(function () {
        var APP_URL = <?php echo json_encode(url('/')); ?>

        $('td.content-file').on("click", 'a', function () {

            $('.spinner-loading').show();
            $('.osb-detail').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));
            console.log($(this).attr('data-url'));
            $.ajax({
                url: $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $('.spinner-loading').hide();
                    $('.osb-detail').html(data[0]).fadeIn();
                }
            });

        });
    });
    

</script>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_batch_file.blade.php ENDPATH**/ ?>