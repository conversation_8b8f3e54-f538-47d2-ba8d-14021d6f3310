<?php $__env->startSection('header'); ?>
    <!-- Search Form -->
    <form id="carianform" action="<?php echo e(url('/find/gfmas')); ?>/<?php echo e($type); ?>/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ... ">
        </div>
    </form>
    <!-- END Search Form -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if($result != null && count($result) > 0): ?>
   
        
            

       
    <div class="block">
        <div class="block-title panel-heading" style = "background-color: #FCFA66;">
            <h1><i class="fa fa-building-o"></i> <strong>Senarai Apive Fail </strong><small>Has<PERSON> : <?php echo e($carian); ?></small></h1>
            <a href="<?php echo e(url('/trigger/gfmas/apive')); ?>/?ep_no=<?php echo e($carian); ?>"  target="_blank" 
               class="btn btn btn-default pull-right"  style="margin:5px;">Trigger APIVE</a>
        </div>
        
        
        <?php if($result && count($result) > 0 ): ?>
        <?php $__currentLoopData = $result; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $obj): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="block collapse panel-xml" id="row_<?php echo e($obj["key"]); ?>">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> <?php echo e($obj["FileName"]); ?></h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup"><?php echo e($obj["contents"]); ?></code>
            </pre>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
            
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">FileName</th>
                        <th class="text-center">StatusSent</th>
                        <th class="text-center">Transaction Date</th>
                        <th class="text-center">Status Desc</th>
                        <th class="text-center">TransID</th>
                        <th class="text-center">Response 1GFMAS</th>
                        <th class="text-center">Details Link</th>
                    </tr>
                </thead>
                <tbody>
                <?php $__currentLoopData = $result; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $obj): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="text-center text-success">
                            <a href="javascript:void(0)" data-toggle="collapse" data-target="#row_<?php echo e($obj["key"]); ?>" ><?php echo e($obj["FileName"]); ?></a>
                            </td>
                        <td class="text-center"><?php echo e($obj["StatusSent"]); ?></td>
                        <td class="text-center"><?php if($obj["InfoDetails"] != null): ?><?php echo e($obj["InfoDetails"]->trans_date); ?><?php endif; ?></td>
                        <td class="text-center"><?php if($obj["InfoDetails"] != null): ?><?php echo e($obj["InfoDetails"]->status_desc); ?><?php endif; ?></td>
                        <td class="text-center"><?php if($obj["InfoDetails"] != null): ?><?php echo e($obj["InfoDetails"]->trans_id); ?><?php endif; ?></td>
                        <?php if($obj["ApoveFileName"] != null): ?>
                        <td class="text-center text-success"><?php echo e($obj["StatusApove"]); ?></td>
                        <?php endif; ?>
                        <?php if($obj["AperrFileName"] != null): ?>
                        <td class="text-center text-danger"><strong><?php echo e($obj["StatusApove"]); ?></strong></td>
                        <?php endif; ?>
                        <td class="text-center">
                            <?php if($obj["ApoveFileName"] != null): ?>
                            <a class="text-success" href="<?php echo e(url('/find/osb/batch/file')); ?>/<?php echo e($obj["ApoveFileName"]); ?>" target="_blank"><?php echo e($obj["ApoveFileName"]); ?></a>
                            <?php endif; ?>
                            <br />
                            <?php if($obj["AperrFileName"] != null): ?>
                            <strong>
                            <a class="text-danger" href="<?php echo e(url('/find/osb/batch/file')); ?>/<?php echo e($obj["AperrFileName"]); ?>" target="_blank" ><?php echo e($obj["AperrFileName"]); ?></a>
                            </strong>
                            <?php endif; ?>
                        </td>
                            
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                </tbody>
            </table>
            <br />
        </div>

    </div>

                
    </div>
        
    <?php elseif($result != null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                    <small>Tidak dijumpai!</small>
                </h1>
            </div>
        </div>
    <?php else: ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Carian Apive (MasterDataVendor) Fail<br>
                    <small>Masukkan eP No. pada carian diatas...</small>
                </h1>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\apive.blade.php ENDPATH**/ ?>