<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

   
    /**
     * Handle a login request to the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request)
    {
        
        $this->validateLogin($request);
        
        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if ($this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        
        if ($this->attemptLogin($request)) {
             
            return $this->sendLoginResponse($request);
        }
        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }
    
    /**
     * Attempt to log the user into the application.
     * Overwrite using login to suiteCRM.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        //$user = DB::table('users')->where('user_name',$request->username)->first();
        
        $user = User::where('user_name',$request->user_name)->first();
        if(!$user){
            return false;
        }
        
        $user_hash = $user->user_hash;
        $password_md5 = md5($request->password);

        if($user_hash[0] != '$' && strlen($user_hash) == 32) {
            // Old way - just md5 password
           if(strtolower(md5($request->password)) == $user_hash){
               $this->guard()->loginUsingId($user->id);
               $this->logLastLogin($user);
                return true;
            }
        }
        if(crypt(strtolower($password_md5), $user_hash) == $user_hash){
            $this->guard()->loginUsingId($user->id);
            $this->logLastLogin($user);
            return true;
        }
        return false;
         
    }
    
    protected function logLastLogin($user){
        
        $userLogin = DB::connection('mysql_ep_support')->table('ep_login_history')
                     ->where('user_id',$user->id)->first();
        
        if($userLogin == null){
            DB::connection('mysql_ep_support')
                ->insert('insert into ep_login_history  
                    (user_id,username,last_login) 
                    values (?, ?, ?)', 
                    [   
                        $user->id, 
                        $user->user_name,
                        Carbon::now()  
                    ]);
        }else{
            DB::connection('mysql_ep_support')->table('ep_login_history')
                    ->where('user_id', $user->id)
                    ->update([
                        'last_login' => Carbon::now()
                    ]);
        }
    }
    
    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function validateLogin(Request $request)
    {
        $this->validate($request, [
            $this->username() => 'required', 'password' => 'required',
        ]);
    }
    
    
    /**
     * Get the login username to be used by the controller.
     *
     * @return string
     */
    public function username()
    {
        return 'user_name';
    }
    
    
    /**
     * Log the user out of the application.
     *
     * @param \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logout(Request $request)
    {
        $this->guard()->logout();

        $request->session()->flush();

        $request->session()->regenerate();

        return redirect('/login');
    }
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest', ['except' => 'logout']);
    }
}
