<?php $__env->startSection('header'); ?>
    <div class="header-section">
        <h1>
            <i class="gi gi-charts"></i>Report<br><small>Payment Statistics</small>
        </h1>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if($dataList): ?>
    <div class="row">
        <div class="col-md-6">
            <!-- Large Widget (Active Color Theme Light) -->
            <div class="widget">
                <div class="widget-advanced widget-advanced-alt">
                    <!-- Widget Header -->
                    <div class="widget-header text-center themed-background-muted-light">
                        <h3 class="widget-content text-left pull-left animation-pullDown">
                            <strong>Percentage Earn (0.4%)</strong><br>
                            <small><?php echo e($dataList[0]->year); ?></small>
                        </h3>
                        <!-- Flot Charts (initialized in js/pages/widgetsStats.js), for more examples you can check out http://www.flotcharts.org/ -->
                        <div id="chart-widget1" class="chart" style="padding: 0px; position: relative;"><canvas class="flot-base" style="direction: ltr; position: absolute; left: 0px; top: 0px; width: 627px; height: 360px;" width="627" height="360"></canvas><canvas class="flot-overlay" style="direction: ltr; position: absolute; left: 0px; top: 0px; width: 627px; height: 360px;" width="627" height="360"></canvas></div>
                    </div>
                    <!-- END Widget Header -->

                    <!-- Widget Main -->
                    <div class="widget-main">
                        <div class="row text-center">
                            <div class="col-xs-4">
                                <h5 class="animation-hatch">Highest<br><strong>RM <?php echo e($dataList[0]->highest_earn_amount); ?><br> (<?php echo e(ucfirst($dataList[0]->highest_earn_month)); ?>)</strong></h5>
                            </div>
                            <div class="col-xs-4">
                                <h5 class="animation-hatch">Current Month<br><strong>RM <?php echo e($dataList[0]->current_earn); ?></strong></h5>
                            </div>
                            <div class="col-xs-4">
                                <h5 class="animation-hatch">Total to Date<br><strong>RM <?php echo e($dataList[0]->summ_all); ?></strong></h5>
                            </div>
                        </div>
                    </div>
                    <!-- END Widget Main -->
                </div>
            </div>
            <!-- END Large Widget (Active Color Theme Light) -->
        </div>
        <div class="col-md-6">
        <?php if($dataStats): ?>
            <!-- Large Widget (Active Color Theme Dark) -->
            <div class="widget">
                <div class="widget-advanced widget-advanced-alt">
                    <!-- Widget Header -->
                    <div class="widget-header text-center themed-background-muted-light">
                        <h3 class="widget-content text-left pull-left animation-pullDown">
                            <strong>Payment Statistics</strong><br>
                            <small><?php echo e($dataList[0]->year); ?></small>
                        </h3>
                        <!-- Flot Charts (initialized in js/pages/widgetsStats.js), for more examples you can check out http://www.flotcharts.org/ -->
                        <div id="chart-widget2" class="chart"></div>
                    </div>
                    <!-- END Widget Header -->

                    <div class="legend">
                        <div style="position: absolute; width: 140px; height: 85px; top: 100px; left: 30px; background-color: rgb(255, 255, 255); opacity: 0.85;"></div>
                        <table style="position:absolute;top:100px;left:30px;;font-size:smaller;color:#545454">
                            <tbody>
                            <tr>
                                <td class="legendColorBox">
                                    <div style="border:1px solid #ccc;padding:1px">
                                        <div style="width:4px;height:0;border:5px solid #f1c40f;overflow:hidden"></div>
                                    </div>
                                </td>
                                <td class="legendLabel">Processing Fee</td>
                            </tr>
                            <tr>
                                <td class="legendColorBox">
                                    <div style="border:1px solid #ccc;padding:1px">
                                        <div style="width:4px;height:0;border:5px solid #c0392b;overflow:hidden"></div>
                                    </div>
                                </td>
                                <td class="legendLabel">Registration Fee</td>
                            </tr>
                            <tr>
                                <td class="legendColorBox">
                                    <div style="border:1px solid #ccc;padding:1px">
                                        <div style="width:4px;height:0;border:5px solid #27ae60;overflow:hidden"></div>
                                    </div>
                                </td>
                                <td class="legendLabel">Softcert Fee</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Widget Main -->
                    <div class="widget-main">
                        <div class="row text-center">
                            <div class="col-xs-3">
                                <h5 class="animation-hatch">Total Current Month<br><strong>RM <?php echo e(number_format($dataStats[0]->current_month_total,2)); ?></strong></h5>
                            </div>
                            <div class="col-xs-3">
                                <h5 class="animation-hatch">Total to Date<br><strong>RM <?php echo e(number_format($dataStats[0]->sum_all,2)); ?></strong></h5>
                            </div>
                            <div class="col-xs-6">
                                <div class="col-xs-4">
                                    <h5 class="animation-hatch">Processing<br><strong>RM <?php echo e(number_format($dataStats[0]->sum_process,2)); ?></strong></h5>
                                </div>
                                <div class="col-xs-4">
                                    <h5 class="animation-hatch">Registration<br><strong>RM <?php echo e(number_format($dataStats[0]->sum_register,2)); ?></strong></h5>
                                </div>
                                <div class="col-xs-4">
                                    <h5 class="animation-hatch">Softcert<br><strong>RM <?php echo e(number_format($dataStats[0]->sum_softcert,2)); ?></strong></h5>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Widget Main -->
                </div>
            </div>
            <!-- END Large Widget (Active Color Theme Dark) -->
            <?php endif; ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="table-responsive" style="background: white;">
                <div id="response" class="table-options clearfix">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <table id="datatable-payment-percentage" class="table table-vcenter table-striped">
                    <thead>
                    <tr>
                        <th class="text-center">No.</th>
                        <th class="text-center">Year</th>
                        <th class="text-center">Month</th>
                        <th class="text-center">Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php $__currentLoopData = $dataList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                            <td class="text-center"><?php echo e($data->year); ?></td>
                            <td class="text-center"><?php echo e(strtoupper($data->month)); ?></td>
                            <td class="text-center">RM <?php echo e(number_format($data->sum_amount,2)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-6">
            <?php if($dataStats): ?>
            <div class="table-responsive" style="background: white;">
                <div id="response" class="table-options clearfix">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <table id="datatable-payment-stats" class="table table-vcenter table-striped">
                    <thead>
                    <tr>
                        <th class="text-center">No.</th>
                        <th class="text-center">Year</th>
                        <th class="text-center">Month</th>
                        <th class="text-center">Bill Type</th>
                        <th class="text-center">Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php $__currentLoopData = $dataStats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $indexKey => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="text-center"><?php echo e(++$indexKey); ?></td>
                            <td class="text-center"><?php echo e($data->year); ?></td>
                            <td class="text-center"><?php echo e(date('F', mktime(0, 0, 0, $data->month, 10))); ?></td>
                            <td class="text-center"><?php echo e($data->bill_type); ?></td>
                            <td class="text-center">RM <?php echo e(number_format($data->total_amt,2)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<script type="text/javascript">

    App.datatables();
    /* Initialize Datatables */
    $('#datatable-payment-percentage').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 5,
        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']]
    });

    $('#datatable-payment-stats').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 5,
        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']]
    });

    // Get the elements where we will attach the charts
    var chartWidget1 = $('#chart-widget1');
    var chartWidget2 = $('#chart-widget2');

    var dataSumAmount = <?php echo json_encode($dataSumAmount); ?>;
    
    // Array with month labels used in both charts
    var chartMonths = [[1, 'January'], [2, 'February'], [3, 'March'], [4, 'April'], [5, 'May'], [6, 'June'], [7, 'July'], [8, 'August'], [9, 'September'], [10, 'October'], [11, 'November'], [12, 'December']];

    var dataProcessFee = <?php echo json_encode($statProcessFeeAmt); ?>;
    var dataRegisterFee = <?php echo json_encode($statRegisterFeeAmt); ?>;
    var dataSoftcertFee = <?php echo json_encode($statSoftcertFeeAmt); ?>;

    // Widget 1 Chart
    $.plot(chartWidget1,
        [
            {
                data: dataSumAmount,
                lines: {show: true, fill: false},
                points: {show: true, radius: 6, fillColor: '#2980b9'}
            }
        ],
        {
            colors: ['#34495e'],
            legend: {show: false},
            grid: {borderWidth: 0, hoverable: true, clickable: true},
            yaxis: {show: false},
            xaxis: {show: true, ticks: chartMonths}
        }
    );

    // Widget 2 Chart
    $.plot(chartWidget2,
        [
            {
                data: dataProcessFee,
                lines: {show: true, fill: false},
                points: {show: true, radius: 6, fillColor: '#f1c40f'}
            },
            {
                data: dataRegisterFee,
                lines: {show: true, fill: false},
                points: {show: true, radius: 6, fillColor: '#e74c3c'}
            },
            {
                data: dataSoftcertFee,
                lines: {show: true, fill: false},
                points: {show: true, radius: 6, fillColor: '#2ecc71'}
            }
        ],
        {
            colors: ['#f39c12', '#c0392b', '#27ae60'],
            legend: {show: true},
            grid: {borderWidth: 0, hoverable: true, clickable: true},
            yaxis: {show: false},
            xaxis: {show: true, ticks: chartMonths}
        }
    );

    // Create our number formatter.
    var formatter = new Intl.NumberFormat('en-MY', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2,
        // the default value for minimumFractionDigits depends on the currency
        // and is usually already 2
    });

    // Creating and attaching a tooltip to both charts
    var previousPoint = null, ttlabel = null;
    chartWidget1.bind('plothover', function(event, pos, item) {

        if (item) {
            if (previousPoint !== item.dataIndex) {
                previousPoint = item.dataIndex;

                $('#chart-tooltip').remove();
                var x = item.datapoint[0], y = item.datapoint[1];

                // Get xaxis label
                var monthLabel = item.series.xaxis.options.ticks[item.dataIndex][1];

                ttlabel = '<strong>' + formatter.format(y) + '</strong> in <strong>' + monthLabel + '</strong>';

                $('<div id="chart-tooltip" class="chart-tooltip">' + ttlabel + '</div>')
                    .css({top: item.pageY - 50, left: item.pageX - 50}).appendTo("body").show();
            }
        }
        else {
            $('#chart-tooltip').remove();
            previousPoint = null;
        }
    });

    chartWidget2.bind('plothover', function(event, pos, item) {

        if (item) {
            if (previousPoint !== item.dataIndex) {
                previousPoint = item.dataIndex;

                $('#chart-tooltip').remove();
                var x = item.datapoint[0], y = item.datapoint[1];

                // Get xaxis label
                var monthLabel = item.series.xaxis.options.ticks[item.dataIndex][1];

                ttlabel = '<strong>' + formatter.format(y) + '</strong> in <strong>' + monthLabel + '</strong>';

                $('<div id="chart-tooltip" class="chart-tooltip">' + ttlabel + '</div>')
                    .css({top: item.pageY - 50, left: item.pageX - 50}).appendTo("body").show();
            }
        }
        else {
            $('#chart-tooltip').remove();
            previousPoint = null;
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\payment_stat.blade.php ENDPATH**/ ?>