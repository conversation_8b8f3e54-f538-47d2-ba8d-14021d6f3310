<?php

namespace Tests\Feature;

// use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    /**
     * Test that the landing page loads successfully.
     */
    public function test_the_landing_page_loads_successfully(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertSee('Backend Service Active');
        $response->assertSee('Console Commands & Scheduled Tasks');
    }

    /**
     * Test that login redirects to external system.
     */
    public function test_login_redirects_to_external_system(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(302);
        $response->assertRedirect();
    }
}
