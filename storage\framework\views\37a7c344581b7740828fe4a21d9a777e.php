<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e(config('app.name', 'Laravel')); ?> - Backend Service</title>
    <link rel="stylesheet" href="<?php echo e(asset('css/landing.css')); ?>">
    <meta name="robots" content="noindex, nofollow">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Backend Service Active</h1>
            <p><?php echo e(config('app.name', 'Laravel Application')); ?> - Console Commands & Scheduled Tasks</p>
        </div>

        <div class="status-grid">
            <div class="status-card success">
                <h3>Application Status</h3>
                <p class="status-value">✅ Running</p>
                <p>Backend services are operational</p>
            </div>

            <div class="status-card <?php echo e($database_status === 'Connected' ? 'success' : 'error'); ?>">
                <h3>Database Connection</h3>
                <p class="status-value"><?php echo e($database_status); ?></p>
                <p>Primary database connectivity</p>
            </div>

            <div class="status-card">
                <h3>Console Commands</h3>
                <p class="status-value"><?php echo e($total_commands); ?></p>
                <p>Available Artisan commands</p>
            </div>

            <div class="status-card">
                <h3>Scheduled Tasks</h3>
                <p class="status-value"><?php echo e($scheduled_tasks); ?></p>
                <p>Active scheduled jobs</p>
            </div>
        </div>

        <div class="info-section">
            <h2>🔧 System Information</h2>
            <ul class="info-list">
                <li><strong>Laravel Version:</strong> <?php echo e(app()->version()); ?></li>
                <li><strong>PHP Version:</strong> <?php echo e(PHP_VERSION); ?></li>
                <li><strong>Environment:</strong> <span class="badge <?php echo e(app()->environment() === 'production' ? 'active' : 'scheduled'); ?>"><?php echo e(strtoupper(app()->environment())); ?></span></li>
                <li><strong>Timezone:</strong> <?php echo e(config('app.timezone')); ?></li>
                <li><strong>Last Updated:</strong> <span class="timestamp"><?php echo e(now()->format('Y-m-d H:i:s T')); ?></span></li>
            </ul>
        </div>

        <div class="info-section">
            <h2>📋 Key Features</h2>
            <ul class="info-list">
                <li><strong>CRM Integration:</strong> Nextgen Government & Supplier sync</li>
                <li><strong>Report Generation:</strong> Daily, weekly, and monthly automated reports</li>
                <li><strong>Case Management:</strong> Auto-close, SLA monitoring, duplicate checking</li>
                <li><strong>Data Processing:</strong> Email handling, file processing, validation</li>
                <li><strong>API Endpoints:</strong> REST APIs for external system integration</li>
                <li><strong>File Downloads:</strong> Report and document download services</li>
            </ul>
        </div>

        <div class="info-section">
            <h2>🔗 Available Services</h2>
            <ul class="info-list">
                <li><strong>File Downloads:</strong> 
                    <a href="/crm/casedetail/download/" style="color: #3498db;">CRM Case Reports</a>, 
                    <a href="/aspect/report/download/" style="color: #3498db;">Aspect Reports</a>
                </li>
                <li><strong>Data Sync APIs:</strong> User, Organization, and Supplier synchronization</li>
                <li><strong>CRM Integration:</strong> Case updates and management</li>
                <li><strong>Document Services:</strong> MOF Certificate and attachment downloads</li>
            </ul>
        </div>

        <?php if(app()->environment() !== 'production'): ?>
        <div class="info-section">
            <h2>🛠️ Development Tools</h2>
            <ul class="info-list">
                <li><strong>Artisan Commands:</strong> <code>php artisan list</code></li>
                <li><strong>Schedule List:</strong> <code>php artisan schedule:list</code></li>
                <li><strong>Queue Worker:</strong> <code>php artisan queue:work</code></li>
                <li><strong>Log Monitoring:</strong> <code>php artisan pail</code></li>
            </ul>
        </div>
        <?php endif; ?>

        <div class="footer">
            <p>This is a backend-only Laravel application focused on console commands and scheduled tasks.</p>
            <p>Frontend components have been removed for optimal performance and security.</p>
            <p><small>Generated at <?php echo e(now()->format('Y-m-d H:i:s T')); ?></small></p>
        </div>
    </div>
</body>
</html>
<?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views/landing.blade.php ENDPATH**/ ?>