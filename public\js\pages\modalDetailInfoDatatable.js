/*
 *  Document   : modalListActionLogDatatable.js
 *  Author     : shamsul
 *  Description: Custom javascript code used in Tables Datatables page
 */

var ModalDetailInfoDatatable = function() {

    return {
        init: function() {
            
            App.datatables();
            /* Initialize Datatables */
            var tableInfoDetailListData =     $('#info-detail-datatable').DataTable({
                    columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                    pageLength: 20,
                    lengthMenu: [[20, -1], [20, 'All']]
            });
            $(document).ready(function() {
            $(document).on("click",'.modal-list-data-action', function(){

                $('.spinner-loading').show();
                $('#info-detail-datatable').html('').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));
                
                $('#url_search_log').val($(this).attr('data-url'));
                $('#search_log').val($(this).attr('data-carian'));
                $('#carian_action_log').val($(this).attr('data-carian'));
                
                var findUrl = $(this).attr('data-url')+$(this).attr('data-carian');
                console.log(findUrl);
                
                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableInfoDetailListData.destroy();

                $.ajax({
                    url: findUrl,
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#info-detail-datatable').html($data).fadeIn();
                        $('.spinner-loading').hide();
                        /* Re-Initialize Datatable */
                        tableInfoDetailListData = $('#info-detail-datatable').DataTable({
                            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                            pageLength: 20,
                            lengthMenu: [[20, -1], [20, 'All']]
                        });
                    }
                });

            });
            
            $('#url_search_button_search').on("click", function(){

                $('.spinner-loading').show();
                $('#info-detail-datatable').html('').fadeIn();

                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableInfoDetailListData.destroy();

                var sendUrl = $('#url_search_log').val() + $('#search_log').val();
    
                $.ajax({
                    url: sendUrl,
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#info-detail-datatable').html($data).fadeIn();
                        $('.spinner-loading').hide();
                        /* Re-Initialize Datatable */
                        tableInfoDetailListData = $('#info-detail-datatable').DataTable({
                            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                            pageLength: 20,
                            lengthMenu: [[20, -1], [20, 'All']]
                        });


                    }
                });

            });
            
            });
        }
    };
}();