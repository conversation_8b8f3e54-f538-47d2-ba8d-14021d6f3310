<?php $__env->startSection('header'); ?>
    <!-- Search Form -->
    <!-- END Search Form -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Content -->
    <?php if(Auth::user()): ?>
        <div class="row">
            <div class="col-lg-4">
                <div class="widget">
                   
                    <div class="widget-extra themed-background-dark">
                        <div class="widget-options">
                            <div class="btn-group btn-group-xs">
                                <a href="javascript:void(0)" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                            </div>
                        </div>
                        <h5 class="widget-content-light">
                            Quotation Tender <strong>Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_qtmonitoring" class="widget-extra-full">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                <div id="dash_outbound" class="widget">
                    <div class="text-center" style="display:none; padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_inbound" class="widget">
                    <div class="text-center" style="display:none; padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_interface" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <!--div id="dash_quartz" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div -->
            </div>
            <div class="col-lg-4">
                <div id="dash_outbound_1gfmas" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_inbound_1gfmas" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB to EJB <strong>Monitor</strong>
                        </h5>
                    </div>
                    <div id="dash_ejbosb">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Item Code Error Monitoring <strong> (GFM-100)</strong>
                        </h5>
                    </div>
                    <div id="dash_checkWsItemCodeErrorInGFM100" style="padding: 20px;">
                        <div class="text-center" style="display:none"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Item Code Error Monitoring <strong> (MMINF / GFM-020)</strong>
                        </h5>
                    </div>
                    <div id="dash_checkWsItemCodeErrorInMMINF" style="padding: 20px;">
                        <div class="text-center"  style="display:none"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Web Service Monitoring <strong> (Validation Exception)</strong>
                        </h5>
                    </div>
                    <div id="dash_ws_validation_exception">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Telnet - <strong>Connection 1GFMAS Batch Server</strong>
                        </h5>
                    </div>
                    <div id="dash_checkConnection" style="padding: 20px;">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">

                
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB <strong>Service Retry</strong>
                        </h5>
                    </div>
                    <div id="dash_osbretry">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB <strong>Notification Retry</strong>
                        </h5>
                    </div>
                    <div id="dash_osbNotifyRetry">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB <strong>Batch Retry</strong>
                        </h5>
                    </div>
                    <div id="dash_osbBatchRetry">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Inbound Error File Monitoring <strong> (Return Error File)</strong>
                        </h5>
                    </div>
                    <div id="dash_checkFileErrorInbound" style="padding: 20px;">
                        <div class="text-center" style="display:none"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
            </div>
        </div>

    <?php endif; ?>
    
    <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div id="fetch_btn" class="row" style="padding: 0 15px 15px 0; display: none;">
                        <div class="pull-right">
                            <a href="<?php echo e(url("/list/1gfmas/folder")); ?>" target="_blank" class="btn btn-sm btn-primary toggle-bordered enable-tooltip" title="" data-original-title="Fetch from 1GFMAS Out Folder"><i class="gi gi-disk_save"></i> Trigger Batch</a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="table-responsive">
                                <table id="basic-datatable" class="table table-striped table-vcenter">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    <!-- END Content -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
    <!-- Load and execute javascript code used only in this page -->

    <script>
        var APP_URL = <?php echo json_encode(url('/')); ?>

        
        App.datatables();
        /* Initialize Datatables */
        var tableListData =     $('#basic-datatable').DataTable({
                columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        });
            
            
        $(document).ready(function () {
            
            $('.widget').on("click",'.modal-list-data-action', function(){
                
                $('.spinner-loading').show();
                $('#basic-datatable').html('').fadeIn();
                
                $('#modal-list-data-header').text($(this).attr('data-title'));
                if($(this).attr('data-url') === '/list/1gfmas/pending/batch/1gfmas-outbound'){
                    $('#fetch_btn').show();
                }
                
                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();
                
                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#basic-datatable').html($data).fadeIn();
                        
                        /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                            pageLength: 10,
                            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                        });
                        
                        $('.spinner-loading').hide();
                    }
                });
                
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkQtMonitoring',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_qtmonitoring').hide().html($data).fadeIn();
                }
            });
            
            //onload apive outbound widget
            /*
            $.ajax({
                url: APP_URL + '/dashboard/apive/outbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound').hide().html($data).fadeIn();
                    $(".pie-chart").easyPieChart();
                }
            });
            */
           
            //onload quartz widget
            /*
            $.ajax({
                url: APP_URL + '/dashboard/quartz',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_quartz').hide().html($data).fadeIn();
                }
            });
            */
           
            //onload check ejbosb
            $.ajax({
                url: APP_URL + '/dashboard/osbretry',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_osbretry').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/osbnotifyretry',
                success: function (data) {
                    $data = $(data);
                    $('#dash_osbNotifyRetry').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/osbbatchretry',
                success: function (data) {
                    $data = $(data);
                    $('#dash_osbBatchRetry').hide().html($data).fadeIn();
                }
            });
            //onload apive outbound widget
            /*
            $.ajax({
                url: APP_URL + '/dashboard/apove/inbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_inbound').hide().html($data).fadeIn();
                }
            });
            */
            $.ajax({
                url: APP_URL + '/dashboard/1gfmas/outbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_1gfmas').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/1gfmas/inbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_inbound_1gfmas').hide().html($data).fadeIn();
                }
            });
            //onload apive diinterfacelog widget
            $.ajax({
                url: APP_URL + '/dashboard/apive/diinterfacelog',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_interface').hide().html($data).fadeIn();
                }
            });
            //onload check connection
            $.ajax({
                url: APP_URL + '/dashboard/checkConnection',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkConnection').hide().html($data).fadeIn();
                }
            });

            
            /*
            $.ajax({
                url: APP_URL + '/dashboard/checkFileErrorInbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkFileErrorInbound').hide().html($data).fadeIn();
                }
            });
            */
           
            /*
            $.ajax({
                url: APP_URL + '/dashboard/checkWsItemCodeErrorInGFM100',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkWsItemCodeErrorInGFM100').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkWsItemCodeErrorInMMINF',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkWsItemCodeErrorInMMINF').hide().html($data).fadeIn();
                }
            });
            */
            
            /*
             * Load too long. disable first 
            $.ajax({
                url: APP_URL + '/dashboard/checkwsvalidation',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_ws_validation_exception').hide().html($data).fadeIn();
                }
            });
            */
          
            //onload check ejbosb
            /* Load too long. disable first  
            $.ajax({
                url: APP_URL + '/dashboard/checkejbosb',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_ejbosb').hide().html($data).fadeIn();
                }
            });
            */
            
            /*
            //interval: apive outbound widget
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/apive/outbound',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_outbound').html($data);
                        $(".pie-chart").easyPieChart();
                    }
                });
            }, 300000);
            */
           
            //interval: quartz widget
            /*
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/quartz',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_quartz').html($data);
                    }
                });
            }, 30000);
            */
           
           /*
            //interval: apove inbound widget
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/apove/inbound',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_inbound').hide().html($data).fadeIn();
                    }
                });
            }, 300000);
            */
           
           
            //interval: apive diinterfacelog widget
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/apive/diinterfacelog',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_interface').html($data);
                    }
                });
            }, 300000);
            //interval: check connection
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkConnection',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkConnection').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //
            
            /*
            //interval: check ejb osb
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkejbosb',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_ejbosb').hide().html($data).fadeIn();
                    }
                });
            }, 840000); //14min
            */
           
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/osbretry',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_osbretry').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
        
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/osbnotifyretry',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_osbNotifyRetry').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/osbbatchretry',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_osbBatchRetry').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
            
            /* 
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkwsvalidation',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_ws_validation_exception').hide().html($data).fadeIn();
                    }
                });
            }, 840000); //14min
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkFileErrorInbound',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkFileErrorInbound').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkWsItemCodeErrorInGFM100',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkWsItemCodeErrorInGFM100').hide().html($data).fadeIn();
                    }
                });
            }, 1020000); //17min
            
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkWsItemCodeErrorInMMINF',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkWsItemCodeErrorInMMINF').hide().html($data).fadeIn();
                    }
                });
            }, 1020000); //17min
            
            */
            
        });
    </script>
    <!-- Load and execute javascript code used only in this page -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\dashboard_gfmas.blade.php ENDPATH**/ ?>