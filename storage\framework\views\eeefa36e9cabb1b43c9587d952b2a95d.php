<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/qt/committee/')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Ahli Jawatan Kuasa Sebut Harga <br>
                <small>Senarai ahli jawatan kuasa bagi sebut harga.</small>
            </h1>
        </div>
    </div>

    <?php if($listdata == null || count($listdata) == 0): ?>
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian Ahli Jawatan Kuasa Sebut Harga </strong>
                        <small>Hasil Carian : <?php echo e($carian); ?></small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">QT NO.</th>
                            <th class="text-center">COMMITTEE ID</th>
                            <th class="text-center">COMMITTEE TYPE ID</th>
                            <th class="text-center">MEMBER CODE</th>
                            <th class="text-center">MEMBER DESC</th>
                            <th class="text-center">MEMBER NAME</th>
                            <th class="text-center">ICNO / PASSPORT</th>
                            <th class="text-center">ROLE CODE</th>
                            <th class="text-center">ROLE DESC</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-center"><?php echo e($data->qt_no); ?></td>
                                <td class="text-center"><?php echo e($data->committee_id); ?></td>
                                <td class="text-left"><?php echo e($data->committee_type_id); ?></td>
                                <td class="text-left"><?php echo e($data->member_code); ?></td>
                                <td class="text-left"><?php echo e($data->member_desc); ?></td>
                                <td class="text-left"><?php echo e($data->member_name); ?></td>
                                <td class="text-left"><?php echo e($data->ic_passport); ?></td>
                                <td class="text-left"><?php echo e($data->role_code); ?></td>
                                <td class="text-left"><?php echo e($data->role_desc); ?></td>
                                
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    <?php endif; ?>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_qt_committees.blade.php ENDPATH**/ ?>