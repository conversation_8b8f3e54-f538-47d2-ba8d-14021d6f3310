@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carianform" @if($type == 'orgcode') action="{{url('/find')}}/{{$type}}/" @else action="{{url('/find/org')}}/{{$type}}/" @endif method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    @if ($orginfo == null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    @if($result == 'notfound')
                        <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                        <small>Rekod tidak dijumpai!</small>
                    @else
                        @if($type == 'orgcode')
                            <i class="gi gi-search"></i>Carian Organisasi Melalui Org Code<br>
                            <small>Masukkan Org Code pada carian diatas...</small>
                        @else
                            <i class="gi gi-search"></i>Carian Organisasi Melalui IC No.<br>
                            <small>Masukkan IC No. pada carian diatas...</small>
                        @endif
                    @endif
                </h1>
            </div>
        </div>
    @endif
    @if($orginfo)
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title  panel-heading" style = "background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Organisasi : {{ $orginfo[0]->org_name }}</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="block">
                            <div class="block-title">
                                <h2>Maklumat Organisasi</h2>
                            </div>
                            @if($orginfo[0]->hierarchy)
                            <ul class="breadcrumb breadcrumb-top">
                                
                                @foreach($orginfo[0]->hierarchy as $hierarchy)
                                    <li><a href="{{ url('/find/orgcode') }}/{{ $hierarchy['org_code'] }}" 
                                           title="{{$hierarchy['org_type']}}" >{{ $hierarchy['org_name'] }}</a></li>
                                @endforeach
                                
                                {{--<li><strong>{{ $orginfo[0]->org_name }}</strong></li>--}}
                            </ul>
                            @endif
                            
                            <h6><strong>{{ $orginfo[0]->org_name }}</strong></h6>
                            <address>
                                <strong>Organization Profile ID </strong> : {{ $orginfo[0]->org_profile_id }}<br />
                                <strong>Organization Code </strong> : {{ $orginfo[0]->org_code }}<br />
                                <strong>Organization Type </strong> : {{ $orginfo[0]->code_desc }}<br />
                                <strong>Effective Date </strong> : {{ $orginfo[0]->eff_date }}<br />
                                {{--<strong>Expired Date </strong> : {{ $orginfo[0]->exp_date }}<br />--}}
                                <strong>Changed Date </strong> : {{ $orginfo[0]->changed_date }}<br />
                            </address>
                        </div>
                    </div>
                </div>
                <!-- START USER DETAILS -->
                @if($listdata)
                <div class="widget">
                    <div class="widget-extra themed-background-dark">
                        <h5 class='widget-content-light'><i class="fa fa-users"></i> <strong>Jumlah Pengguna : {{ count($listdata) }}</strong></h5>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">

                            <div class="table-responsive">
                                <table id="basic-datatable" class="table table-striped table-vcenter">
                                    <thead>
                                    <tr>
                                        <th class="text-center">No.</th>
                                        <th class="text-center">Name</th>
                                        <th class="text-center">Identification No.</th>
                                        <th class="text-center">Role Admin</th>
                                        <th class="text-center">Email</th>
                                        <th class="text-center"></th>
                                    </tr>
                                    </thead>

                                    <tbody>
                                    @foreach ($listdata as  $indexKey => $data)
                                        <tr>
                                            <td class="text-center">{{ ++$indexKey }}</td>
                                            <td class="text-center">{{ $data->fullname }}</td>
                                            <td class="text-center">{{ $data->identification_no }}</td>
                                            <td class="text-center">{{ $data->role_admin }}</td>
                                            <td class="text-center">{{ $data->email }}</td>
                                            <td class="text-center">
                                                <button class="btn btn-info btn-xs"
                                                        data-toggle="collapse"
                                                        data-target="#row_{{$data->user_id}}">
                                                    Details
                                                </button>
                                            </td>
                                        </tr>
                                        <tr id="row_{{$data->user_id}}"  class="collapse">
                                            <td class="text-center" colspan="7">
                                                <div class="block"style="background-color: inherit;">
                                                    <div class="row">
                                                        <div class="col-sm-4">
                                                            <div class="block">
                                                                <div class="block-title">
                                                                    <h2>Maklumat User Login  </h2>
                                                                </div>
                                                                <address class="text-left">
                                                                    <strong>User ID </strong> : {{ $data->user_id  }}<br />
                                                                    <strong>Login ID </strong> : {{ $data->login_id  }}<br />
                                                                    <strong>Email </strong> : {{ $data->email  }}<br />
                                                                    <strong>ICNO </strong> : {{ $data->identification_no  }}<br />
                                                                    <strong>Mobile </strong> : {{ $data->mobile_country }}{{ $data->mobile_area }}{{ $data->mobile_no }}<br />
                                                                    <strong>Last Login Date </strong> : {{ $data->login_date }}<br />
                                                                    <strong>Last Date Changed </strong> :  {{  $data->changed_date }} <br />
                                                                </address>
                                                            </div>
                                                        </div>
                                                        @if(count($data->listUserRole ) > 0)
                                                            <div class="col-sm-4">
                                                                <div class="block">
                                                                    <div class="block-title">
                                                                        <h2>Peranan </h2>
                                                                    </div>
                                                                    <address class="text-left">
                                                                        @foreach ($data->listUserRole as $indexKey => $role)
                                                                            <strong>{{++$indexKey}}) </strong>{{$role->role_name}} <br/>
                                                                        @endforeach
                                                                    </address>
                                                                </div>
                                                            </div>
                                                        @endif
                                                        @if(count($data->listApprover ) > 0)
                                                            <div class="col-sm-4">
                                                                <div class="block">
                                                                    <div class="block-title">
                                                                        <h2>Pelulus </h2>
                                                                    </div>
                                                                    <address class="text-left">
                                                                        @foreach ($data->listApprover as $indexKey => $approver)
                                                                            <strong>{{++$indexKey}}) </strong>{{$approver->group_name}} ({{$approver->group_code}})<br/>
                                                                        @endforeach
                                                                    </address>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

    @endif
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function(){ TablesDatatables.init(); });</script>
@endsection