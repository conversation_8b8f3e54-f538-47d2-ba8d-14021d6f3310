<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/icno')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... IC No.">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>
    <?php if($listdata == null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <?php if($result == 'notfound'): ?>
                        <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                        <small>Rekod tidak dijumpai!</small>
                    <?php else: ?>
                        <i class="gi gi-search"></i>Carian IC No.<br>
                        <small>Masukkan no. IC pada carian diatas...</small>
                    <?php endif; ?>
                </h1>
            </div>
        </div>
    <?php endif; ?>

    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
    <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>ePerolehan (Pembekal) : <?php echo e($data->company_name); ?></strong></h1>
                </div>
                <div class="row">
                    <?php if($data->is_activate_key == true): ?>
                    <div class="col-sm-12">
                        <div class="notify-alert alert alert-success alert-dismissable">
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                            <h4><i class="fa fa-check-circle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4> 
                            Sila semak <a href="javascript:void(0)" class="alert-link">e-mel <?php echo e($data->p_email); ?> di INBOX / SPAM</a> untuk aktifkan login ID.
                            <br /> <br />
                                <strong>Link Activation </strong> :  <a target="_blank" href="<?php echo e($data->link); ?>"><?php echo e($data->link); ?></a> <br />
                                <strong>Activation Key </strong> :  <?php echo e($data->activation_key); ?><br />
                                <strong>Status Success Send </strong> :  <?php echo e($data->is_sent); ?> <br />
                                <strong>Last Date Changed </strong> :  <?php echo e($data->changed_date); ?> <br />
                           
                        </div>
                        
                    </div>
                    <?php endif; ?>
                    <div class="col-sm-4">
                        <div class="block">
                            
                            <div class="block-title">
                                <h2>Maklumat Syarikat  </h2>
                            </div>
 
                            <h6><strong><?php echo e($data->company_name); ?></strong></h6>
                            <address>
                                <strong>Supplier ID </strong> : <?php echo e($data->supplier_id); ?><br />
                                <strong>Business Type </strong> : <?php echo e($data->business_type); ?><br />
                                <strong>SSM No </strong> : <?php echo e($data->reg_no); ?><br />
                                <strong>eP No </strong> : <a href="<?php echo e(url('/find/epno')); ?>/<?php echo e($data->ep_no); ?>" ><?php echo e($data->ep_no); ?></a><br />
                                <strong>Establish Date </strong> : <?php echo e($data->establish_date); ?> <br />
                                <strong>Last Date Changed </strong> : <?php echo e($data->s_changed_date); ?> <br />
                                <strong>Record Status </strong> : <?php echo e($data->s_record_status); ?><br />
                            </address>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="block">

                            <div class="block-title">
                                <h2>Maklumat MOF</h2>
                            </div>
 
                            <address>
                                <strong>MOF NO </strong> : <a href="<?php echo e(url('/find/mofno')); ?>/<?php echo e($data->mof_no); ?>" ><?php echo e($data->mof_no); ?></a><br />
                                <strong>Expired Date </strong> : <?php echo e($data->ma_exp_date); ?><br />
                                <strong>Record Status </strong> : <?php echo e($data->ma_record_status); ?><br />
                            </address>
                        </div>
                    </div>
                    <?php if($data->is_business_network == true): ?>
                    <div class="col-sm-4">
                        <div class="block">

                            <div class="block-title">
                                <h2>Business Network</h2>
                            </div>
 
                            <address>
                                <strong>Federal? </strong> : <?php echo e($data->is_with_federal); ?><br />
                                <strong>State? </strong> : <?php echo e($data->is_with_state); ?><br />
                                <strong>Local Council? </strong> : <?php echo e($data->is_with_statutory); ?><br />
                                <strong>GLC? </strong> : <?php echo e($data->is_with_glc); ?><br />
                                <strong>Others? </strong> : <?php echo e($data->is_with_others); ?><br />
                            </address>
                        </div>
                    </div>
                    <?php endif; ?>
                    
   
                     
     
                </div>
                
                <div class="row">
                    <div class="col-sm-12">

                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">Name</th>
                                        <th class="text-center">Identification No.</th>
                                        <th class="text-center">Email</th>
                                        <th class="text-center">Role</th>
                                        <th class="text-center">Softcert Status</th>
                                        <th class="text-center"></th>
                                    </tr>
                                </thead>

                                <tbody>

                                    <tr>
                                        <td class="text-center"><?php echo e($data->p_name); ?></td>
                                        <td class="text-center"><?php echo e($data->p_identification_no); ?></td>
                                        <td class="text-center"><?php echo e($data->p_email); ?></td>
                                        <td class="text-center"><?php echo e($data->p_ep_role); ?></td>
                                        <td class="text-center"><?php echo e($data->p_is_softcert); ?></td>
                                        <td class="text-center">
                                            <button class="btn btn-info btn-xs"
                                               data-toggle="collapse" data-target="#row_<?php echo e($data->personnel_id); ?>" >
                                                Details</button></td>
                                    </tr>
                                    <tr id="row_<?php echo e($data->personnel_id); ?>" <?php if(strlen($data->p_ep_role) > 0): ?>class="collapsed" <?php else: ?> class="collapse" <?php endif; ?>>
                                        <td class="text-center" colspan="7">
                                            <!-- Customer Addresses Block -->
                                            <div class="block" <?php if($data->p_ep_role == 'MOF_SUPPLIER_ADMIN'): ?>style="background-color: inherit;"<?php endif; ?>>
                                                <div class="row">
                                                    <?php if($data->is_activate_key == true): ?>
                                                    <div class="col-sm-12"  class="text-left">
                                                        <div class="notify-alert alert alert-success alert-dismissable">
                                                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                                            <h4><i class="fa fa-exclamation-triangle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4> 
                                                            Sila semak <a href="javascript:void(0)" class="alert-link">e-mel <?php echo e($data->p_email); ?> di INBOX / SPAM</a> untuk aktifkan login ID.
                                                            <br /> <br />
                                                                <strong>Link Activation </strong> :  <a target="_blank" href="<?php echo e($data->link); ?>"><?php echo e($data->link); ?></a> <br />
                                                                <strong>Activation Key </strong> :  <?php echo e($data->activation_key); ?><br />
                                                                <strong>Status Success Send </strong> :  <?php echo e($data->is_sent); ?> <br />
                                                                <strong>Last Date Changed </strong> :  <?php echo e($data->activation_changed_date); ?> <br />

                                                        </div>

                                                    </div>
                                                    <?php endif; ?>

                                                    <div class="col-sm-4">
                                                        <div class="block">
                                                            <div class="block-title">
                                                                <h2>Maklumat User Login  </h2>
                                                            </div>
                                                            <address class="text-left">
                                                                <strong>User ID </strong> : <?php echo e($data->user_id); ?><br />
                                                                <strong>Login ID </strong> : <?php echo e($data->login_id); ?><br />
                                                                <strong>Email </strong> : <?php echo e($data->email); ?><br />
                                                                <strong>ICNO </strong> : <?php echo e($data->identification_no); ?><br />
                                                                <strong>Record Status </strong> : <?php echo e($data->u_record_status); ?><br />
                                                                <strong>Last Login Date </strong> : <?php echo e($data->login_date); ?><br />
                                                                <strong>Last Date Changed </strong> :  <?php echo e($data->changed_date); ?> <br />

                                                                <br />
                                                                <strong>Peranan</strong> : <br/>
                                                                <?php if(!empty($data->roles)): ?>
                                                                    <?php $__currentLoopData = $data->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php echo e($role->role_code); ?> <br/>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                <?php else: ?>
                                                                Tiada
                                                                <?php endif; ?>
                                                            </address>
                                                        </div>
                                                    </div>

                                                     <div class="col-sm-4">
                                                        <div class="block">
                                                            <div class="block-title">
                                                                <h2>Maklumat Diri  Staf Syarikat </h2>
                                                            </div>
                                                            <address  class="text-left">
                                                                <strong>Personal ID </strong> : <?php echo e($data->personnel_id); ?><br />
                                                                <strong>Designation </strong> : <?php echo e($data->p_designation); ?><br />
                                                                <strong>Email </strong> : <?php echo e($data->p_email); ?><br />
                                                                <strong>Role </strong> : <?php echo e($data->p_ep_role); ?><br />
                                                                <strong>SoftCert Status </strong> : <?php echo e($data->p_is_softcert); ?><br />
                                                                <strong>Is Equity Owner? </strong> : <?php echo e($data->is_equity_owner); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                <strong>Is Contract Signer? </strong> : <?php echo e($data->is_contract_signer); ?><br />
                                                                <strong>Is Authorized? </strong> : <?php echo e($data->is_authorized); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                <strong>Is Contact Person? </strong> : <?php echo e($data->is_contact_person); ?><br />
                                                                <strong>Is Management? </strong> : <?php echo e($data->is_mgt); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                <strong>Is Director? </strong> : <?php echo e($data->is_director); ?><br />
                                                                <strong>Is Bumi? </strong> : <?php echo e($data->is_bumi); ?>&nbsp;&nbsp;,&nbsp;&nbsp;
                                                                <strong>Record Status </strong> : <?php echo e($data->p_record_status); ?><br />
                                                                <strong>Last Date Changed </strong> :  <?php echo e($data->p_changed_date); ?> <br />
                                                            </address>
                                                        </div>
                                                     </div>

                                                    <?php if(count($data->listSoftCert ) > 0): ?>
                                                    <div class="col-sm-4">
                                                        <div class="block">
                                                            <div class="block-title">
                                                                <h2>SoftCert Request</h2>
                                                            </div>

                                                            <?php $__currentLoopData = $data->listSoftCert; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sCert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <address  class="text-left">
                                                                <strong>SoftCert Request ID </strong> : <?php echo e($sCert->softcert_request_id); ?><br /> 
                                                                <?php if($sCert->is_trustgate == true): ?>
                                                                <a target='_blank'
                                                                   style="color:darkblue;font-weight: bolder;text-decoration: underline;"
                                                                   href="https://digitalid.msctrustgate.com/nextgenEP/ePRA/migrate/detail_migrate?epNo=<?php echo e($sCert->ep_no); ?>&icNo=<?php echo e($data->p_identification_no); ?>" >
                                                                    Check TrustGate (<?php if($sCert->is_trustgate_data == true): ?>Expiry on  <?php echo e($sCert->trustgate_expired_date); ?><?php endif; ?>)</a><br />
                                                                <?php endif; ?>
                                                                <strong>Record Status </strong> : <?php echo e($sCert->record_status); ?><br />
                                                                <strong>Date Created </strong> :  <?php echo e($sCert->created_date); ?> <br />
                                                                <strong>Last Date Changed </strong> :  <?php echo e($sCert->changed_date); ?> <br />
                                                                <strong>Using Free SoftCert</strong> :  <?php echo e($sCert->is_free); ?> <br />
                                                                
                                                                <?php if(strlen($sCert->cert_serial_no)>0): ?>
                                                                <strong>Cert Serial NO. </strong> : <?php echo e($sCert->cert_serial_no); ?><br />
                                                                <strong>Cert Issuer </strong> : <?php if($sCert->cert_issuer=='T'): ?> TrustGate <?php else: ?> Digicert <?php endif; ?><br />
                                                                <strong>Cert Valid From </strong> : <?php echo e($sCert->valid_from); ?><br />
                                                                <strong>Cert Valid To</strong> : <?php echo e($sCert->valid_to); ?><br />
                                                                <strong>Cert Record Status </strong> : <?php echo e($sCert->pdc_record_status); ?><br />
                                                                <strong>Cert Last Date Changed </strong> :  <?php echo e($sCert->pdc_changed_date); ?> <br />
                                                                <?php else: ?>
                                                                <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />
                                                                <?php endif; ?>
                                                            </address>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                </div>
                                            </div> 
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>


                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>





                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
                    </div>
                </div>

            </div>
            <!-- END Customer Addresses Block -->
            
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>


<?php $__env->stopSection(); ?>


<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\user_details.blade.php ENDPATH**/ ?>