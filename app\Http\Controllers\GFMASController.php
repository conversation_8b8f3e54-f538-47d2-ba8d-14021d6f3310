<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\SSHService;
use Carbon\Carbon;
use Illuminate\Support\Facades\SSH;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Models\EpSupportActionLog;

class GFMASController extends Controller {

    use SupplierService;
    use OSBService;
    use ProfileService;
    use FulfilmentService;
    use SSHService;


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function wsOSBLog() {
        return view('list_1gfmas', [
            'listdata' => null,
            'listXml' => null,
            'carian' => '']);
    }

    public function searchWsOSBLog($search) {
        $list = array();
        
        /** Ignored search 18 digit refer Item Kod **/
//        if(strlen(trim($search)) == 18){
//            return view('list_1gfmas', [
//                        'listdata' => $list,
//                        'carian' => $search]);
//        }
        
        $checkPRCR = substr($search, 0, 2);
        if($checkPRCR == 'PR' || $checkPRCR == 'CR'){
            $search = $this->getDocNoByPrCr($search);
            if($search == null){
                return view('list_1gfmas', [
                        'listdata' => array(),
                        'carian' => $search]);
            }
        }
        $list = $this->getListOSB1GFMASWebServiceDetails($search);
        return view('list_1gfmas', [
            'listdata' => $list,
            'carian' => $search]);
    }
    
    protected function setApiveData($resSent){
        $collects = collect([]);
        $apoveFileName = $this->getApoveFilename($resSent->remarks_1);
        $apoveResp = $this->getApove1GFMASResponseByApivefile($apoveFileName);
        $status = "No logs info. sent to 1GFMAS";
        if($resSent && strlen($resSent->remarks_1)> 0 ){
            $status = "Successfully sent file to 1GFMAS";    
        }
        //$statusApove = "1GFMAS is not updated yet. Checking file is not found :: ".$apoveFileName;
        $statusApove = "";
        $filenameApove = "";
        $filenameAperr = "";
        if($apoveResp && strlen($apoveResp->file_name)> 22 ){
            $statusApove = "Record has been updated in 1GFMAS as SUCCESS (".$apoveResp->file_name.")";    
            $filenameApove = $apoveResp->file_name;
        }else{
            $aperrFileName = $this->getAperrFilename($resSent->remarks_1);
            $aperrResp = $this->getAperr1GFMASResponseByApivefile($aperrFileName);
            if($aperrResp && strlen($aperrResp->file_name)> 22 ){
               $statusApove = $statusApove."\nRecord has been updated in 1GFMAS as ERROR (".$aperrResp->file_name.")";    
               $filenameAperr = $aperrResp->file_name;
            }
        }
        
        if($statusApove == ""){
            $statusApove = "1GFMAS is not update yet";
        }
        $collects->put('key', str_replace(".GPG", "",$resSent->remarks_1));
        $collects->put('FileName', $resSent->remarks_1);
        $collects->put('StatusSent', $status);
        $collects->put('StatusApove', $statusApove);
        $collects->put('InfoDetails', $resSent);
        $collects->put('ApoveDetails', $apoveResp);
        $collects->put('ApoveFileName', $filenameApove);
        $collects->put('AperrFileName', $filenameAperr);
        return $collects;
    }
    
    public function getApiveDetails($epNo){
        if($epNo == 'insert_epno'){
            return view('apive', [
                'type' => 'apive',
                'carian' => null,
                'result' => null
            ]);
        }
        
        $checkEPNO = substr($epNo, 0, 2);
        if($checkEPNO != 'eP'){
            return view('apive', [
                'type' => 'apive',
                'carian' => $epNo,
                'result' => collect([])
            ]);
        }
        $collection = collect([]);
        
        $listResSentFiles = $this->getApive1GFMASResponseByEpNo($epNo);
        if($listResSentFiles != null && count($listResSentFiles) > 0){
            foreach ($listResSentFiles as $resSent){
                $collects = $this->setApiveData($resSent);
                $collection->push($collects);
            }
        }
        
        
        //Off carian Temp Folder
        /* 
        $commands  = [
            'cd /batch/Temp',
            'grep -E "'.$epNo.'" 1000APIVE40000120*.GPG',
        ];
        SSH::into('portal')->run($commands, function($line) use ($epNo,&$collection)  {
            $data = $line.PHP_EOL;
            if (strpos($data, $epNo) !== false) {
                $arrayData  = (explode("\n",$data));
                foreach($arrayData  as $str){
                    $filenameTemp = substr($str, 0, 31); 
                    $filename = str_replace(":", "", $filenameTemp);
                    $checkExisted = $collection->where('FileName',$filename)->count();
                    if($checkExisted == 0){
                        $collects = collect([]);
                        if(strlen($filename) > 25){
                            $resSent = $this->getApive1GFMASResponse($filename);
                            if($resSent && strlen($resSent->logging_id) > 1 ){
                                $collects = $this->setApiveData($resSent);
                                $collection->push($collects);
                            }
                        } 
                    }
                }
            }
        });
        */
        
        if(count($collection) > 0 ){
            /** Getting content file **/
            foreach($collection as $data){
                $filename = $data["FileName"];
                $epNoCheck = $data["InfoDetails"]->remarks_3;
                if($epNoCheck != null && strlen($epNoCheck) > 11){
                    $transID = $data["InfoDetails"]->trans_id;
                    $contentsObj = $this->getApiveContents1GFMAS($transID);
                    $data['contents'] = $contentsObj->file_data;
                }else{
                    $contents = SSH::into('portal')->getString("/batch/Temp/". $filename);
                    $data['contents'] = $contents;
                }
            }
        }
        
        return view('apive', [
            'type' => 'apive',
            'carian' => $epNo,
            'result' => $collection,
        ]);
    }
    
    public function checkConnectionGFMAS(){
        $data = array();
        $commands  = [
            "echo 'exit' | sftp -oPort=2022 eperolehan@10.38.206.73",
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            //var_dump($result);
            array_push($data,trim($result));
        });
        return view('telnet', [
            'result' => $data,
        ]);
    }
    
    public function logGfmasDetailsPoCo(){
        return view('list_gfmas', [
            'listdata' => null,
            'type' => 'poco',
            'carian' => '']);
    }
    public function getLogGfmasDetailsPoCo($docNo) {
        $list = $this->getListOSB1GFMASWebServiceDetails($docNo);
        return $this->getLogGfmasDetails($list,$docNo,'poco');
        
    }

    public function logGfmasDetailsPrCr(){
        return view('list_gfmas', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'prcr',
            'carian' => '']);
    }
    public function getLogGfmasDetailsPrCr($docNo) {
        $pocono = $this->getPoCoNo($docNo);
        if($pocono != null){
            $list = $this->getListOSB1GFMASWebServiceDetails($pocono);
            return $this->getLogGfmasDetails($list,$docNo,'prcr');
        }
        return $this->getLogGfmasDetails(array(),$docNo,'prcr');
    }

    public function logGfmasDetailsMMINF(){
        return view('list_gfmas', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'mminf',
            'carian' => '']);
    }
    public function getLogGfmasDetailsMMINF($itemCode) {
        $list = $this->getListOSB1GFMASWebServiceDetails($itemCode);
        return $this->getLogGfmasDetails($list,$itemCode,'mminf');
    }

    protected function getLogGfmasDetails($list, $docNo, $type) {
        $listXml = array();
        foreach($list as $obj){
            if($obj->trans_type == 'IBReq'){
                array_push($listXml,$obj);
            }
        }
        return view('list_gfmas', [
            'listdata' => $list,
            'listXml' => $listXml,
            'type' => $type,
            'carian' => $docNo]);
    }
    
    public function listLogOSB($name) {
        $list = array();
        if($name == 'checkWsValidation'){
          $list = $this->getListWsValidationException();  
        }
        if($name == 'checkWsOsbEjbNoReceiver'){
          $list = $this->getListWsErrNoEJBReceiver();
        }
        if($name == 'checkWsOsbEjbTimeOut'){
          $list = $this->getListWsErrEJBTimeOut(); 
        }
        if($name == 'checkWsItemCodeErrorInGFM100'){
          $list = $this->getListWsErrItemCodeInGFM100(); 
        }
        if($name == 'checkWsItemCodeErrorInMMINF'){
          $list = $this->getListWsErrItemCodeInMMINF();
        }
        return view('list_osb', [
            'listdata' => $list ]);
    }
 
    public function listPendingBatchHTML($type){
        $dataList = array();
        if($type == 'ep-inbound'){
            $dataList = $this->getList1GfmasFolderIN();
        }else if($type == 'ep-outbound'){
            $dataList = $this->getList1GfmasFolderOUT();
        }else if($type == '1gfmas-inbound'){ // Refer to Server 1GFMAS  Folder IN
            $dataList = $this->getList1GfmasServerFolderIN();
        }else if($type == '1gfmas-outbound'){ // Refer to Server 1GFMAS  Folder OUT
            $dataList = $this->getList1GfmasServerFolderOUT();
        }else if($type == 'ep-apove-inbound'){ 
            $dataList = $this->getListAPOVEInFolder();
        }else if($type == 'ep-apive-outbound'){
            $dataList = $this->getListAPIVEOutFolder();
        }else{
           return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type of Batch is not define properly!  $type</th>
                            </tr>
                        </thead>"; 
        }
        
        return $this->populateBatchFilesHTML($dataList);
    }
    
    public function listBatchHTML($type,$serviceCode,$transDate){
        $dataList = array();
        if($type == 'ep-file-error-inbound'){
            $list = $this->getListFileErrProcessing($serviceCode, $transDate);
            $collection = collect($list);
            $dataList = $collection->pluck('file_name');
        }
        
        if(count($dataList) > 0){
            return $this->populateBatchFilesHTML($dataList);
        }else{
            return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type of Batch is not define properly!</th>
                            </tr>
                        </thead>";
        }
    }
    
    protected function populateBatchFilesHTML($dataList){
        
        $html =    "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>FileName</th>
                        </tr>
                    </thead>";
        $html = $html."<tbody>";
        $counter = 0;
        foreach ($dataList as $value){
                $url = url('/find/osb/batch/file');
                $data = "
                    <tr>
                        <td class='text-center'><strong>".++$counter."</strong></td>
                        <td class='text-left'><strong><a href='$url/$value' target='_blank'>
                                        $value</a></strong></td>
                    </tr>";
                $html = $html.$data;
        }
        $html = $html."<tbody>";
        return $html;
    }

    public function mminfTriggerView() {
        return view('mminf_trigger', [
            'result' => null
        ]);
    }

    public function searchPreMminf(Request $request) {
        $searchType = $request->searchType;
        $tabName = '';

        $result = null;
        if($searchType == 'search_docno'){
            $docNo = $request->doc_no;
            $itemCode = $request->item_code;
            $tabName = 'search-tab-docno';
            if($docNo) {
                $typeDoc   = substr($docNo, 0, 2);
                if($typeDoc == 'PO' || $typeDoc == 'CO'){
                    $docObj = $this->getDocNoPRPOorCRCO($docNo);
                    if($docObj){
                       $docNo =  $docObj->fr_doc_no;
                    }
                }
                $listReqItem = $this->getMminfReqItemId($docNo, $itemCode);
                $listRequestItemId = collect($listReqItem)->pluck('request_item_id');
                if(count($listRequestItemId) > 0) {
                    $result = $this->getMminfSearchDetailByListReqItemId($listRequestItemId);
                }

            }
        } elseif ($searchType == 'search_reqitemid'){
            $reqItemId = $request->req_item_id;
            $tabName = 'search-tab-reqitemid';
            if($reqItemId) {
                $listDetail = $this->getMminfSearchDetail($reqItemId);
                $result = $listDetail;
            }
        }

        if($result == null) {
            $result = 'notfound';
        }

        return view('mminf_trigger', [
            'result' => $result,
            'msg' => null,
            'tabName' => $tabName,
            'formSearch' => $request->all()
        ]);
    }
    
    public function mminfTrigger(Request $request) {
        
        $reqItemId = $request->request_item_id;
        $curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(4);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'><trig:ScRequestItem><trig:requestItemId>$reqItemId</trig:requestItemId><trig:changedDate>$now</trig:changedDate></trig:ScRequestItem></trig:ScRequestItemCollection></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $urlIdentity = "http://192.168.63.205:7011/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],$reqItemId);
        $result = null;
        SSH::into('osb')->run($commands, function($line) use (&$msg, &$reqItemId, &$result) {});

        sleep(3);
        $result = $this->getMminfSearchDetail($reqItemId);

        $checkChangeDate = Carbon::parse($result[0]->changed_date);

        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'success';
            EpSupportActionLog::updateActionLog($actionLog, 'Completed');
        } else {
            $status = 'fail';
            EpSupportActionLog::updateActionLog($actionLog, 'Failed');
        }
        return array('status'=>$status,'value'=>$result[0]->changed_date);

    }

    public function  mminfTriggerByDocNo(Request $request) {
        $docNo = $request->request_item_id;
        Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo);
        $typeDoc   = substr($docNo, 0, 2);
        if($typeDoc == 'PO' || $typeDoc == 'CO'){
            $docObj = $this->getDocNoPRPOorCRCO($docNo);
            if($docObj){
               $docNo =  $docObj->fr_doc_no;
            }
        }
        $listReqItem = $this->getMminfReqItemId($docNo, null);
        $listRequestItemId = collect($listReqItem)->pluck('request_item_id');

        Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId));

        $resReqItems = $this->getMminfSearchDetailByListReqItemId($listRequestItemId);

        $reqItem = $resReqItems->first();
        $checkChangeDate = Carbon::parse($reqItem->changed_date);

        if($checkChangeDate->diffInMinutes(Carbon::now()) > 10) {
            /* Trigger Item All to changed date */
            $status = $this->mminfTriggerByListID($listRequestItemId);
            
            if($status == 'Completed'){
                Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId). ' status: success');
                return array('status'=>'success','value'=>'','total_items'=>count($listRequestItemId));
            }else{
                $status_error = 'Trigger all items failed!';
                Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId). ' status: '.$status_error);
                return array('status'=>'fail','value'=>'','total_items'=>count($listRequestItemId),'status_error'=>$status_error);
            }
            
        }else{
            $status_error = 'Failed because already updated';
            Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId). ' status: '.$status_error);
            return array('status'=>'fail','value'=>'','total_items'=>count($listRequestItemId),'status_error'=>$status_error);
        }
            
        
    }
    
    protected function mminfTriggerByListID($listReqItemId) {
        //$reqItemId = $request->request_item_id;
        //$curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(10);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $ScRequestItemXML = "";
        foreach ($listReqItemId as $reqItemId){
            $ScRequestItemXML = $ScRequestItemXML.
                                "<trig:ScRequestItem>
                                    <trig:requestItemId>$reqItemId</trig:requestItemId>
                                 </trig:ScRequestItem>";
        }

        $xmlContents = ""
                . "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                    . "<trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'>"
                        . $ScRequestItemXML
                    . "</trig:ScRequestItemCollection>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $urlIdentity = "http://192.168.63.205:7011/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],json_encode($listReqItemId));

        SSH::into('osb')->run($commands, function($line){$line.PHP_EOL; });
        
        sleep(10);
        
        $resReqItems = $this->getMminfSearchDetailByListReqItemId($listReqItemId);

        $reqItem = $resReqItems->first();
        $checkChangeDate = Carbon::parse($reqItem->changed_date);

        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'Completed';
        }else{
            $status = 'Failed';
        }
        
        EpSupportActionLog::updateActionLog($actionLog, $status);
        Log::info(self::class .'>'.__FUNCTION__. ': '.$status);
        
        return $status;
    }
    
    public function displayDashboardMminfDiInterface() {
        $data = $this->getDashboardMminfDiInterfaceLog();

        return view('include.mminf_dash_diinterface', [
            'data' => $data
        ]);

    }

    public function displayDashboardMminfQuartz() {
        $dataFired = $this->getDashboardMMinfQuartz();
        $dataExecution = $this->getDashboardMMinfQuartzExecution();

        if ($dataFired) {
            $dataFired[0]->prev_fired = Carbon::parse($dataFired[0]->prev_fired)->format('d/m/Y H:i:s A');
            $dataFired[0]->next_fired = Carbon::parse($dataFired[0]->next_fired)->format('d/m/Y H:i:s A');
            if($dataFired[0]->trigger_state === 'WAITING') {
                $dataFired[0]->state_color = 'label-warning';
            } elseif ($dataFired[0]->trigger_state === 'BLOCKED') {
                $dataFired[0]->state_color = 'label-danger';
            } else {
                $dataFired[0]->state_color = 'label-info';
            }
        }

        if ($dataExecution) {
            $dataExecution[0]->finish_execution_date = Carbon::parse($dataExecution[0]->finish_execution_date)->format('d/m/Y H:i:s A');
        }

        return view('include.mminf_dash_quartz', [
            'dataFired' => $dataFired,
            'dataExecution' => $dataExecution
        ]);
    }

    public function apiveTriggerView(Request $request) {
        $array = array();
        if(!isset($request->ep_no)){
            $array =  array(
                'ep_no' => ''
            );
        }else{
            $array =  array(
                'ep_no' => $request->ep_no
            );
        }
        return view('apive_trigger', [
            'result' => null,
            'formSearch' => $array 
        ]);
    }

    public function searchPreApive(Request $request) {
        $ePno = $request->ep_no;

        $result = null;
        if($ePno) {
            $listDetail = $this->getApiveTriggerInfo($ePno);
            $result = $listDetail;
        }

        if($result == null) {
            $result = 'notfound';
        }

        return view('apive_trigger', [
            'result' => $result,
            'msg' => null,
            'formSearch' => $request->all()
        ]);
    }

    public function apiveTrigger(Request $request) {
        $ePno = $request->ep_no;
        $curChangedDate = $request->current_changed_date;

        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerAPIVEInput xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/TriggerAPIVE'><trig:ep_no>$ePno</trig:ep_no></trig:TriggerAPIVEInput></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $urlIdentity = "http://192.168.63.205:7011/TriggerAPIVE/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        $actionLog =  EpSupportActionLog::createActionLog('TriggerAPIVE','Web Service',$commands[0],$request->ep_no);
        $result = null;
        SSH::into('osb')->run($commands, function($line) use (&$msg, &$result) {});

        sleep(3);
        $result = $this->getApiveTriggerInfo($ePno);
        if($result){
            $updatedChangedDate = $result[0]->changed_date;
            if($curChangedDate == $updatedChangedDate) {
                $status = 'fail';
                EpSupportActionLog::updateActionLog($actionLog, 'Failed');
            } else {
                $status = 'success';
                EpSupportActionLog::updateActionLog($actionLog, 'Completed');
            }
        } else {
            $status = 'fail';
            EpSupportActionLog::updateActionLog($actionLog, 'Failed');
        }

        return array('status'=>$status,'value'=>$updatedChangedDate);

    }

    public function fetchFromFolderDisplay() {
        return view('fetch_1gfmas', [
            'dataList' => null
        ]);
    }

    public function fetchFromFolderList() {
        $dataList = $this->getList1GfmasServerFolderOUT();

        $html = "   <thead>
                    <tr>
                        <th class=\"text-center\">No.</th>
                        <th class=\"text-center\">File Name</th>
                    </tr>
                    </thead>
                    <tbody>";
        $count = 1;
        foreach ($dataList as $data) {
            $html .= "
                        <tr>
                            <td class=\"text-center\"> ". $count++ ." </td>
                            <td class=\"text-center\">". $data ."</td>
                        </tr>";
        }

        $html .= "
                    </tbody>";
        return $html;
    }

    public function fetch1GfmasOutFolder(Request $request) {
        $processId = $request->process_id;

        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerBatchInput xmlns:trig='http://www.ep.gov.my/Schema/1-0/TriggerBatch'><trig:ProcessId>$processId</trig:ProcessId><trig:StuckAt>1GFMAS</trig:StuckAt></trig:TriggerBatchInput></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $url = "http://192.168.63.205:7012/TriggerBatch/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl ".$url." --header  ".$urlHeader."  -d ".$xmlContents,
        ];

        $msg = '';
        SSH::into('osb')->run($commands, function ($line) use (&$msg) {
            $data = $line . PHP_EOL;
            $p = xml_parser_create();
            xml_parse_into_struct($p, $data, $vals, $index);
            xml_parser_free($p);
            foreach($vals as $val){
                if($val["tag"] == 'TRIG:STATUS') {
                    if(array_key_exists('value', $val)) $msg = $val["value"];
                }
            }
        });

        if($msg == 'No file to be processed'){
            $state = 'false';
        } else {
            $state = 'true';
        }

        return array('msg'=>$msg,'state'=>$state);

    }

}
