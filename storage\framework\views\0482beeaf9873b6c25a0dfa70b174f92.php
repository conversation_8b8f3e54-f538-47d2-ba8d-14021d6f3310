<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/uom')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Status Menunggu Bayaran<br>
                <small>Senarai transaksi bayaran yang belum disahkan oleh MOLPAY. Sila semak setiap ORDER ID di MOLPAY. 
                    Jika wujud, sila tekan button SYNC di MOLPAY. Jika tidak wujud, sila hapuskan senarai dibawah. 
                    <br />Sila pastikan 'Date Created' setiap transaksi untuk anda semak sudah melebihi 45 minit. </small>
            </h1>
        </div>


    </div>

    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Senarai Pembayaran Pembekal </strong>
                    </h1>
                    <div class="block-options pull-right action-today">
                        <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                           data-toggle="modal" data-url="<?php echo e(url('/support/report/log/task-sync-payment')); ?>/<?php echo e(Carbon\Carbon::now()->format('Y-m-d')); ?>" 
                           data-title="List Action Remove Payment Not Listed in MOLPAY by Today ">View Today Action</a>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">SUPPLIER</th>
                            <th class="text-center">EP_NO</th>
                            <th class="text-center">BILL_TYPE</th>
                            <th class="text-center">ORDER_ID</th>
                            <th class="text-center">PAYMENT_AMOUNT</th>
                            <th class="text-center">BILL_NO</th>
                            <th class="text-center">BILL_DATE</th>
                            <th class="text-center">DATE_CREATED</th>
                            <th class="text-center">ACTION</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="tr-<?php echo e($data->order_id); ?>">
                                <td class="text-left"><?php echo e($data->company_name); ?></td>
                                <td class="text-center"><a href="<?php echo e(url("/find/epno")); ?>/<?php echo e($data->ep_no); ?>" target="_blank"><?php echo e($data->ep_no); ?></a></td>
                                <td class="text-center"><?php echo e($data->bill_type); ?> - <?php echo e(App\Services\EPService::$BILL_TYPE[$data->bill_type]); ?></td>
                                <td class="text-center"><?php echo e($data->order_id); ?></td>
                                <td class="text-center"><?php echo e($data->payment_amt); ?></td>
                                <td class="text-center"><?php echo e($data->bill_no); ?></td>
                                <td class="text-center"><?php echo e(Carbon\Carbon::parse($data->bill_date)->format('Y-m-d')); ?></td>
                                <td class="text-center"><?php echo e($data->created_date); ?></td>
                                <td class="text-center action_table">
                                    <div class="btn-group btn-group-xs">
                                        <a class="btn btn-primary action_table_hapus"  title="" data-original-title="Remove this! Not found in MOLPAY."
                                             href="#modal_confirm_hapus" data-toggle="modal" data-id="<?php echo e($data->order_id); ?>"><i class="hi hi-trash"></i></a>
                                      
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
        
    <?php endif; ?>

    <div id="modal_confirm_hapus" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"> Adakah anda pasti untuk hapuskan rekod ini <i class="fa fa-question"></i></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                                <?php echo e(csrf_field()); ?>

                                
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Order ID</label>
                                    <div class="col-md-9">
                                        <p class="form-control-static"><input type="text" id="hapus_order_id" value="" readonly /></p>
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="button" class="btn btn-sm btn-primary action_payment_hapus"><i class="hi hi-trash"></i> YA</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="fa fa-close"></i> TIDAK</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    
    <?php echo $__env->make('_shared._modalListLogAction', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>

<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    $('td.action_table').on("click",'a.action_table_hapus', function(){
        var orderID = $(this).attr('data-id');
        console.log("ORDER ID: "+orderID);
        $("#hapus_order_id").val(orderID);
    });
    $('div.form-actions').on("click",'button.action_payment_hapus', function(){
        
        var orderID =$("#hapus_order_id").val();
        var csrf = $("input[name=_token]").val();
        $.ajax({
            url: "/support/molpay/payment/"+orderID,
            method : "POST",
            dataType : "json",
            data : {"_token":csrf,"order_id":orderID},
            context: document.body
        }).done(function(resp) {
            if(resp.status === '00'){
                $classTR = orderID;
                $("tr.tr-"+orderID).hide();
            }
            
            $('#modal_confirm_hapus').modal('hide');
        });
    });

</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_pending_payment.blade.php ENDPATH**/ ?>