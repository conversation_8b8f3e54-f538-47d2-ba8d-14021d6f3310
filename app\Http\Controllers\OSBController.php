<?php

namespace App\Http\Controllers;

use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;
use App\Services\Traits\FulfilmentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class OSBController extends Controller {

    use OSBService;
    use SSHService;
    use FulfilmentService;


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function OSBLog() {
        return view('list_osb', [
            'listdata' => null,
            'carian' => '']);
    }
    
    public function searchOSBLog($search) {
        $list = array();
        
        /** Ignored search 18 digit refer Item Kod **/
//        if(strlen(trim($search)) == 18){
//            return view('list_osb', [
//                        'listdata' => $list,
//                        'carian' => $search]);
//        }
        
        $checkPRCR = substr($search, 0, 2);
        if($checkPRCR == 'PR' || $checkPRCR == 'CR'){
            $search = $this->getDocNoByPrCr($search);
            if($search == null){
                return view('list_osb', [
                        'listdata' => $list,
                        'carian' => $search]);
            }
        }
        
        $listTransId = $this->getTransIdOSBLoggingByRemarks($search);
        if(count($listTransId) > 0){
            $list = $this->searchOSBLogDetailsReqResByTransId($listTransId);
        }
        return view('list_osb', [
            'listdata' => $list,
            'carian' => $search]);
    }

    public function batchOSBLog() {
        return view('list_batch_file', [
            'listdata' => null,
            'objFile' => null,
            'carian' => '']);
    }
    
    public function searchBatchOSBLog($fileName) {
        
        $list = array();
        $objFile = $this->getBatchFileLog($fileName);
        $objFileCompletedProcess = $this->getDetailDiInterfaceLog($fileName);
        if($objFile){
            $listTransId = array($objFile->trans_id);
            $list = $this->searchOSBLogDetailsAllByTransId($listTransId);
        }
        
        return view('list_batch_file', [
            'listdata' => $list,
            'objFile' => $objFile,
            'objFileCompletedProcess' => $objFileCompletedProcess,
            'carian' => $fileName]);
    }
    
    public function decryptFile($fileName) {
        $objFile = $this->getBatchFileLog($fileName);
        
        if($objFile){
            return $result = $this->wsDecryptFile1GFMAS($objFile);
        }
        $result = collect([]);
        $result->push('Fail not found!  '.$fileName);
        return $result;
    }
    
    
    public function searchListErrorTransactionOSB(Request $request){
        //Ignored carian
        //return "Maaf! Carian ini dibatalkan. Gangguan, proses query ini sangat lambat.";
        
        $listServiceCode = DB::connection('oracle_nextgen_rpt')->table('osb_service')
                ->whereNotIn('service_code',['NA'])
                ->where('record_status',1)->get();
        
        $formRequest = collect($request->all());
        $dataSearch = collect([]);
        if(count($formRequest) == 0){
            return view('list_osb_error', [
            'listdata' => array(),
            'listServiceCode' => $listServiceCode
                ]);
        }else{
            Validator::make($request->all(), [
                'service_code' => 'required',
                'date_start' => 'required | date_format:"Y-m-d"|after:"2017-12-31"'
            ])->validate();
            
            $dateStartObj =  Carbon::parse($request->date_start);
            $dateStart =  $dateStartObj->format('Y-m-d');
            $dateEnd =  $dateStartObj->addDay()->format('Y-m-d');
            $dataSearch->put('service_code', $request->service_code);
            $dataSearch->put('date_start', $dateStart);
            $dataSearch->put('date_end', $dateEnd);
        }

        $list = $this->getListWsTransactionErrorBySearch($dataSearch);
        
        /** Enable this, to show error with not resolve **/
        /*
        $listRemark = collect($list)->pluck('trans_id')->unique();
        $listSuccessTrans = DB::connection('oracle_nextgen_rpt')->table('osb_logging')
                ->whereIn('trans_id',$listRemark)
                ->where('status','S')
                ->where('service_code',$request->service_code)
                ->where('trans_type','IBRes')
                ->get();
      
        if(count($listSuccessTrans)> 0){
            $listExclude = $listSuccessTrans->pluck('trans_id')->unique();
            //dd($listExclude) ;       
            $filtered = $listRemark->diffKeys($listExclude);
            $list = collect($list)->whereIn('trans_id',$filtered);
        }
        */
        
        
        session()->flashInput($request->input());
        return view('list_osb_error', [
            'listdata' => $list,
            'listServiceCode' => $listServiceCode  
                ]);
        
    }
    
}
