<?php $__env->startSection('header'); ?>
    <!-- Search Form -->
    <form id="carianform" action="<?php echo e(url('/find')); ?>/<?php echo e($type); ?>/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();"
                   placeholder="Carian di sini ... ">
        </div>
    </form>
    <!-- END Search Form -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if(Auth::user()): ?>
        <?php if($result != null && array_key_exists('name', $result)): ?>
            <div class="block">
                <!-- Block Title -->
                <div class="block-title">
                    <h2><strong><?php echo e(($result['name'])); ?></strong></h2>
                </div>
                <!-- END Block Title -->

                <!-- Block Content -->
                <address>
                    <strong>IC No. : </strong><?php echo e(($result['icno'])); ?><br/>
                    <strong>Gender : </strong><?php echo e(($result['gender'])); ?><br/>
                    <strong>Address 1 : </strong><?php echo e(($result['addr1'])); ?><br/>
                    <strong>Address 2 : </strong><?php echo e(($result['addr2'])); ?><br/>
                    <strong>Address 3 : </strong><?php echo e(($result['addr3'])); ?><br/>
                    <strong>Postcode : </strong><?php echo e(($result['postcode'])); ?><br/>
                    <strong>City : </strong><?php echo e(($result['city'])); ?><br/>
                    <strong>State Code : </strong><?php echo e(strtoupper(($result['statecode']))); ?><br/>
                    <strong>Phone No. : </strong><?php echo e(strtoupper(($result['mobile']))); ?><br/>
                    <strong>Email: </strong><?php echo e(strtoupper(($result['email']))); ?><br/>
                </address>
                <!-- END Block Content -->
            </div>
        <?php elseif($result != null): ?>
            <div class="content-header">
                <div class="header-section">
                    <h1>
                        <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                        <small>Tidak dijumpai!</small>
                    </h1>
                </div>
            </div>
        <?php else: ?>
            <div class="content-header">
                <div class="header-section">
                    <h1>
                        <i class="gi gi-search"></i>Carian identity JPN<br>
                        <small>Masukkan no. IC pada carian diatas...</small>
                    </h1>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\identity.blade.php ENDPATH**/ ?>