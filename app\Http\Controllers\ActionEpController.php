<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\SupplierFullGrantService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\OSBService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\EpSupportActionLog;
use App\Services\EPService;
use Illuminate\Support\Facades\DB;

/**
 * This controller class will update into DB eP. Please be careful
 */
class ActionEpController extends Controller {

    use SupplierService;
    use ProfileService;
    use SupplierFullGrantService;
    use OSBService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function searchUserPersonnelDetails($applId,$personnelId){
        $personnelDetails = null;
        $personnelDetailsInProgress = null;
        if($applId == null || $personnelId == null){
            return view('user_personnel_patch', [
                'data' => $personnelDetails
                    ]);
        }else{
            $personnelDetails = $this->getSMSupplierUsersDetailsByPersonnel($applId, $personnelId);
            if($personnelDetails){
                $this->populatePersonnelUserData($personnelDetails);

                $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrant($personnelDetails->supplier_id,$personnelDetails->p_identification_no);
                if(count($listinProgressSuppProcessAppl) > 0){
                  $inAppl = $listinProgressSuppProcessAppl[0];
                  if($inAppl){
                    $personnelDetailsInProgress = $this->getSMSupplierUsersDetailsInProgressApplByPersonnel($inAppl->appl_id, $inAppl->personnel_id);
                    $this->populatePersonnelUserData($personnelDetailsInProgress);
                  }
                }
            }
            
        }
        //return json_encode($personnelDetails);
        return view('user_personnel_patch', [
            'data' => $personnelDetails,
            'data_inprogress' => $personnelDetailsInProgress,    
                ]);
    }
    
    public function updateUserPersonnelDetails($applId,$personnelId){
        $resultStatus = null;
        $resultDesc = null;
        $isSuccessSave = false;
        $patchingProcess = '';

        if($applId == null || $personnelId == null){
            return view('user_personnel_patch', [
                'data' => null,
                    ]);
        }else{
            Validator::make(request()->all(), [
                'selected_process_id' => 'required',
                'remarks' => 'required',
            ])->validate();
            
            $personnelDetails = $this->getSMSupplierUsersDetailsByPersonnel($applId, $personnelId);
            $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrant(
                    $personnelDetails->supplier_id,$personnelDetails->p_identification_no);
            
            $processId = request()->selected_process_id;
            $actionTypeLog = 'Script';
            $actionName = 'PatchData';
            $logs = collect([]);
            
            $parameters =  collect([]);            
            $parameters->put("remarks", request()->remarks);
            
            switch ($processId) {
                case "personnel_softcert_4":
                    if($personnelDetails->p_is_softcert == 0 
                            || $personnelDetails->p_is_softcert == 1 
                            || $personnelDetails->p_is_softcert == 3 
                            || $personnelDetails->p_is_softcert == 7){
                        $patchingProcess = EPService::$USER_PROCESS_PATCHING['personnel_softcert_4']['name'];
                        
                        $updateFields = ['is_softcert' => 4];
                        $parameters->put("patching", $patchingProcess);
                        
                        $this->saveTransactionSmPersonnel($isSuccessSave, $updateFields, $personnelId, $listinProgressSuppProcessAppl, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');
                        
                    }
                    break;
                case "personnel_softcert_1":
                    if($personnelDetails->p_is_softcert == 4 
                            || $personnelDetails->p_is_softcert == 3 
                            || $personnelDetails->p_is_softcert == 7){
                        $patchingProcess = EPService::$USER_PROCESS_PATCHING['personnel_softcert_1']['name'];
                        
                        $updateFields = ['is_softcert' => 1];
                        $parameters->put("patching", $patchingProcess);
                        
                        $this->saveTransactionSmPersonnel($isSuccessSave, $updateFields, $personnelId, $listinProgressSuppProcessAppl, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');
                        
                    }
                    break;
                case "personnel_1":
                    if($personnelDetails->p_record_status == 0 
                            || $personnelDetails->p_record_status == 9 ){
                        $patchingProcess = EPService::$USER_PROCESS_PATCHING['personnel_1']['name'];
                        
                        $updateFields = ['record_status' => 1];
                        $parameters->put("patching", $patchingProcess);
                        
                        $this->saveTransactionSmPersonnel($isSuccessSave, $updateFields, $personnelId, $listinProgressSuppProcessAppl, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');
                        
                    }
                    break;  
                case "personnel_9":
                    if($personnelDetails->p_record_status == 0 
                            || $personnelDetails->p_record_status == 1 ){
                        $patchingProcess = EPService::$USER_PROCESS_PATCHING['personnel_9']['name'];
                        
                        $updateFields = ['record_status' => 9];
                        $parameters->put("patching", $patchingProcess);
                        
                        $this->saveTransactionSmPersonnel($isSuccessSave, $updateFields, $personnelId, $listinProgressSuppProcessAppl, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');
                        
                    }
                    break;     
                default:
                    dd('Process Not found');
            }

            $latestPersonnelDetails = $this->getSMSupplierUsersDetailsByPersonnel($applId, $personnelId);
            $this->populatePersonnelUserData($latestPersonnelDetails);
            
            $latestPersonnelDetailsInProgress = null;
            $latestListinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrant($personnelDetails->supplier_id,$personnelDetails->p_identification_no);
            if(count($latestListinProgressSuppProcessAppl) > 0){
              $latestInAppl = $latestListinProgressSuppProcessAppl[0];
              if($latestInAppl){
                $latestPersonnelDetailsInProgress = $this->getSMSupplierUsersDetailsInProgressApplByPersonnel($latestInAppl->appl_id, $latestInAppl->personnel_id);
                $this->populatePersonnelUserData($latestPersonnelDetailsInProgress);
              }
            }
            
                                    
            if($isSuccessSave == true){
                $resultStatus = 'success';
                $resultDesc = "Kemaskini maklumat personnel > $patchingProcess  berjaya disimpan.";
            }else{
                $resultStatus = 'failed';
                $resultDesc = "Kemaskini maklumat personnel > $patchingProcess  gagal disimpan.";
            }
        }
        //return json_encode($personnelDetails);
        return view('user_personnel_patch', [
                'data' => $latestPersonnelDetails,
                'data_inprogress' => $latestPersonnelDetailsInProgress,
                'result_status' => $resultStatus,
                'result_desc'   => $resultDesc,
            ]);
    }
    
    
    public function saveTransactionSmPersonnel(&$isSuccessSave,$updateFields,$personnelId, $listinProgressSuppProcessAppl,&$logs){
        //If failed, it will rollback
        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use (&$isSuccessSave,$updateFields,$personnelId, $listinProgressSuppProcessAppl,&$logs) {

            $logQuery = $this->updateSMPersonnel($personnelId,$updateFields);
            $logs->put('action_1',$logQuery);

            if(count($listinProgressSuppProcessAppl) > 0){
                $inAppl = $listinProgressSuppProcessAppl[0];
                $logQueryInProg = $this->updateSMPersonnel($inAppl->personnel_id,$updateFields);
                $logs->put('action_2',$logQueryInProg);
            }

            $isSuccessSave = true;
        });
        return $isSuccessSave;
    }
    
   

}
