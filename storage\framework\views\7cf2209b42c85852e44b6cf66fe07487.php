<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/trans/track/docno/')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Tracking Diari<br>
                <small>Carian Dokumen No. &raquo;  Simple Quote (SQ),Quatation Tender (QT),  Request Note (RN) , LOA Number (LA), Contract No (CT), 
                    Purchase Request (PR), Contract Request (CR), Purchase Order (PO) and Contract Order (CO) only.</small>
            </h1>
        </div>
    </div>

    <?php if($listdata == null || count($listdata) == 0): ?>
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading" style = "background-color: #FCFA66;">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : <?php echo e($carian); ?></strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Tracking Diari </strong>
                        <small>Hasil Carian : <?php echo e($carian); ?></small>
                    </h1>
                </div>
                
                <?php if(isset($supplier) && $supplier != null): ?>
                <address>
                    <strong>Supplier Name </strong> : <?php echo e($supplier->company_name); ?><br />
                    <strong>Business Type </strong> : <?php echo e($supplier->business_type); ?>  &raquo;  (<?php echo e(App\Services\EPService::$BUSINESS_TYPE[$supplier->business_type]); ?>)<br />
                    <strong>SSM No </strong> : <?php echo e($supplier->reg_no); ?><br />
                    <strong>eP No </strong> : <?php echo e($supplier->ep_no); ?>  <a target="_blank" href="<?php echo e(url('/find/gfmas/apive/')); ?>/<?php echo e($supplier->ep_no); ?>" >Check Apive</a><br />
                    <strong>MOF No </strong> : <?php echo e($supplier->mof_no); ?>  <a target="_blank" href="<?php echo e(url('/find/mofno')); ?>/<?php echo e($supplier->mof_no); ?>" >Details Supplier</a><br />
                </address>                
                <?php endif; ?>
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">DOC NO.</th>
                            <th class="text-center">ACTION DESCRIPTION</th>
                            <th class="text-center">ACTION DATE</th>
                            <th class="text-center">ROLE</th>
                            <th class="text-center">STATUS</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-center"><?php echo e($data->doc_type); ?></td>
                                <td class="text-center"><?php echo e($data->doc_no); ?></td>
                                <td class="text-left"><?php echo e($data->action_desc); ?></td>
                                <td class="text-center"><?php echo e($data->actioned_date); ?></td>
                                <td class="text-center"><?php echo e($data->role_code); ?></td>
                                <td class="text-center"><?php echo e($data->status_name); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    <?php endif; ?>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_doc_tracking.blade.php ENDPATH**/ ?>