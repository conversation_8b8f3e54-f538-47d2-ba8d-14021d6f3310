<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for database operations. This is
    | the connection which will be utilized unless another connection
    | is explicitly specified when you execute a query / statement.
    |
    */

    'default' => env('DB_CONNECTION', 'sqlite'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Below are all of the database connections defined for your application.
    | An example configuration is provided for each database system which
    | is supported by Laravel. You're free to add / remove connections.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DB_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
            'busy_timeout' => null,
            'journal_mode' => null,
            'synchronous' => null,
        ],

        'mysql_crm' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_HOST', '***************'),
            'port' => env('DB_MYSQL_CRM_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_DATABASE', 'cdccrm'),
            'username' => env('DB_MYSQL_CRM_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_PASSWORD', 'cDccRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_archieve' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_ARCHIEVE_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_ARCHIEVE_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_ARCHIEVE_DATABASE', 'cdccrm'),
            'username' => env('DB_MYSQL_CRM_ARCHIEVE_USERNAME', 'suppcrm'),
            'password' => env('DB_MYSQL_CRM_ARCHIEVE_PASSWORD', 'Suppcrm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],
        'mysql_invoice_migration' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_HOST', 'localhost'),
            'port' => env('DB_MYSQL_PORT', '3306'),
            'database' => env('DB_MYSQL_DATABASE', 'invoicemigration'),
            'username' => env('DB_MYSQL_USERNAME', 'root'),
            'password' => env('DB_MYSQL_PASSWORD', 'Haikal2612'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],
        'mysql_ep_support' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_SUPPORT_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_SUPPORT_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_SUPPORT_DATABASE', 'ep_support'),
            'username' => env('DB_MYSQL_EP_SUPPORT_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_SUPPORT_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'sqlsrv_cms' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_CMS_HOST', '*************'),
            'port' => env('DB_CMS_PORT', '1433'),
            'database' => env('DB_CMS_DATABASE', 'epprod'),
            'username' => env('DB_CMS_USERNAME', 'cms_read'),
            'password' => env('DB_CMS_PASSWORD', 'cm5_r34d'),
            // 'charset' => 'utf8',
            'prefix' => '',
            //   'prefix_indexes' => true,
        ],

        'sqlsrv_aspect' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_ASPECT_HOST', '************'),
            'port' => env('DB_ASPECT_PORT', '1433'),
            'database' => env('DB_ASPECT_DATABASE', 'detail_epro'),
            'username' => env('DB_ASPECT_USERNAME', 'dbusr'),
            'password' => env('DB_ASPECT_PASSWORD', 'Casb@1234'),
            // 'charset' => 'utf8',
            'prefix' => '',
            // 'prefix_indexes' => true,
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'oracle_epcore_pms' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_EPCORE_PMS_TNS', ''),
            'host'          => env('DB_EPCORE_PMS_HOST', '*************'), // stg
            'port'          => env('DB_EPCORE_PMS_PORT', '1521'),
            'database'      => env('DB_EPCORE_PMS_DATABASE', 'EPCORE.COMMERCEDC.COM.MY'),
            'service_name'  => env('DB_EPCORE_PMS_DATABASE', 'EPCORE.COMMERCEDC.COM.MY'),
            'username'      => env('DB_EPCORE_PMS_USERNAME', 'pms'),
            'password'      => env('DB_EPCORE_PMS_PASSWORD', 'pms'),
            'charset'       => env('DB_EPCORE_PMS_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_EPCORE_PMS_PREFIX', ''),

        ],

        'oracle_nextgen' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_TNS', ''),
            'host'          => env('DB_NEXTGEN_HOST', 'rac-cluster-scan.eperolehan.com.my'), // stg
            'port'          => env('DB_NEXTGEN_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_DATABASE', 'uatapp'),
            'service_name'  => env('DB_NEXTGEN_DATABASE', 'uatapp'),
            'username'      => env('DB_NEXTGEN_USERNAME', 'ngep_uat'),
            'password'      => env('DB_NEXTGEN_PASSWORD', 'ng3p_u4t'),
            'charset'       => env('DB_NEXTGEN_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_PREFIX', ''),

        ],

        'oracle_nextgen_rpt' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_RPT_TNS', ''),
            'host'          => env('DB_NEXTGEN_RPT_HOST', 'rac-cluster-scan.eperolehan.com.my'), // stg
            'port'          => env('DB_NEXTGEN_RPT_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_RPT_DATABASE', 'uatapp'),
            'service_name'  => env('DB_NEXTGEN_RPT_DATABASE', 'uatapp'),
            'username'      => env('DB_NEXTGEN_RPT_USERNAME', 'ngep_uat'),
            'password'      => env('DB_NEXTGEN_RPT_PASSWORD', 'ng3p_u4t'),
            'charset'       => env('DB_NEXTGEN_RPT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_RPT_PREFIX', ''),

        ],

        'oracle_nextgen_fullgrant' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_FULLGRANT_TNS', ''),
            'host'          => env('DB_NEXTGEN_FULLGRANT_HOST', 'rac-cluster-scan.eperolehan.com.my'), // stg
            'port'          => env('DB_NEXTGEN_FULLGRANT_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_FULLGRANT_DATABASE', 'pdsapp'),
            'service_name'  => env('DB_NEXTGEN_FULLGRANT_DATABASE', 'pdsapp'),
            'username'      => env('DB_NEXTGEN_FULLGRANT_USERNAME', 'ngep_pds'),
            'password'      => env('DB_NEXTGEN_FULLGRANT_PASSWORD', 'ng3p_pd5'),
            'charset'       => env('DB_NEXTGEN_FULLGRANT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_FULLGRANT_PREFIX', ''),

        ],


        'oracle_bpm_rpt' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_BPM_RPT_TNS', ''),
            'host'          => env('DB_BPM_RPT_HOST', 'racrpt-cluster-scan.eperolehan.com.my'), // pds
            'port'          => env('DB_BPM_RPT_PORT', '1521'),
            'database'      => env('DB_BPM_RPT_DATABASE', 'pdssoa'),
            'service_name'  => env('DB_BPM_RPT_DATABASE', 'pdssoa'),
            'username'      => env('DB_BPM_RPT_USERNAME', 'pdssoa_soainfra'),
            'password'      => env('DB_BPM_RPT_PASSWORD', 'pd5504'),
            'charset'       => env('DB_BPM_RPT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_BPM_RPT_PREFIX', ''),

        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_SQLSERV_HOST', '************'),
            'port' => env('DB_SQLSERV_PORT', '49430'),
            'database' => env('DB_SQLSERV_DATABASE', 'Ulysses7i_CDC'),
            'username' => env('DB_SQLSERV_USERNAME', 'midware'),
            'password' => env('DB_SQLSERV_PASSWORD', 'midware123'),
            'prefix' => '',
        ],

        'mysql_casb' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CASB_HOST', '**************'),
            'port' => env('DB_MYSQL_CASB_PORT', '3306'),
            'database' => env('DB_MYSQL_CASB_DATABASE', 'casbcrm'),
            'username' => env('DB_MYSQL_CASB_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CASB_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_casb_migration' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CASB_MIGRATION_HOST', '***************'),
            'port' => env('DB_MYSQL_CASB_MIGRATION_PORT', '3306'),
            'database' => env('DB_MYSQL_CASB_MIGRATION_DATABASE', 'migration'),
            'username' => env('DB_MYSQL_CASB_MIGRATION_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CASB_MIGRATION_PASSWORD', 'cRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_crm_ssm' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_SSM_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_SSM_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_SSM_DATABASE', 'crm_ssm'),
            'username' => env('DB_MYSQL_CRM_SSM_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_SSM_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_crm_ssm_dev' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_SSM_DEV_HOST', '***************'),
            'port' => env('DB_MYSQL_CRM_SSM_DEV_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_SSM_DEV_DATABASE', 'casbcrm_ssm_dev'),
            'username' => env('DB_MYSQL_CRM_SSM_DEV_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_SSM_DEV_PASSWORD', 'cRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_crm_ssm_migration' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_MIGRATION_HOST', '***************'),
            'port' => env('DB_MYSQL_CRM_MIGRATION_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_MIGRATION_DATABASE', 'migration'),
            'username' => env('DB_MYSQL_CRM_MIGRATION_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_MIGRATION_PASSWORD', 'cRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_crm_jbal' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_JBAL_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_JBAL_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_JBAL_DATABASE', 'casbcrm_jbal'),
            'username' => env('DB_MYSQL_CRM_JBAL_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_JBAL_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_notify' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_NOTIFY_HOST', '**************'),
            'port' => env('DB_MYSQL_EP_NOTIFY_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_NOTIFY_DATABASE', 'ep_notify'),
            'username' => env('DB_MYSQL_EP_NOTIFY_USERNAME', 'epssadmin'),
            'password' => env('DB_MYSQL_EP_NOTIFY_PASSWORD', 'cDc@2018'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        // Local Dev DB
        // 'mysql_crm_gamuda' => [
        //     'driver' => 'mysql',
        //     'host' => 'localhost',
        //     'port' => '3306',
        //     'database' => 'crm_gamuda',
        //     'username' => 'root',
        //     'password' => 'password',
        //     'charset' => 'utf8mb4',
        //     'collation' => 'utf8mb4_general_ci',
        //     'prefix' => '',
        //     'strict' => true,
        //     'engine' => null,
        //     'modes'  => [
        //         'ONLY_FULL_GROUP_BY',
        //         'STRICT_TRANS_TABLES',
        //         'NO_ZERO_IN_DATE',
        //         'NO_ZERO_DATE',
        //         'ERROR_FOR_DIVISION_BY_ZERO',
        //         'NO_ENGINE_SUBSTITUTION',
        //     ],
        // ],

        // Prod DB
        'mysql_crm_gamuda' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_GAMUDA_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_GAMUDA_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_GAMUDA_DATABASE', 'crm_gamuda'),
            'username' => env('DB_MYSQL_CRM_GAMUDA_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_GAMUDA_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'latin1',
            'collation' => 'latin1_swedish_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        // Local Dev CRM SSM
        // 'mysql_crm_ssm' => [
        //     'driver' => 'mysql',
        //     'host' => 'localhost',
        //     'port' => '3306',
        //     'database' => 'ssm_crm_v3',
        //     'username' => 'root',
        //     'password' => 'password',
        //     'charset' => 'utf8mb4',
        //     'collation' => 'utf8mb4_general_ci',
        //     'prefix' => '',
        //     'strict' => true,
        //     'engine' => null,
        //     'modes'  => [
        //         'ONLY_FULL_GROUP_BY',
        //         'STRICT_TRANS_TABLES',
        //         'NO_ZERO_IN_DATE',
        //         'NO_ZERO_DATE',
        //         'ERROR_FOR_DIVISION_BY_ZERO',
        //         'NO_ENGINE_SUBSTITUTION',
        //     ],
        // ],

        'mysql_crm_vantage' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_VANTAGE_HOST', '***************'),
            'port' => env('DB_MYSQL_CRM_VANTAGE_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_VANTAGE_DATABASE', 'crm_vantage'),
            'username' => env('DB_MYSQL_CRM_VANTAGE_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_VANTAGE_PASSWORD', 'cRm@2017'),
            'charset' => 'latin1',
            'collation' => 'latin1_swedish_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run on the database.
    |
    */

    'migrations' => [
        'table' => 'migrations',
        'update_date_on_publish' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as Memcached. You may define your connection settings here.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_database_'),
            'persistent' => env('REDIS_PERSISTENT', false),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];
