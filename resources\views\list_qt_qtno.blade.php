@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carianform" action="{{url('/find/qt')}}/{{$type}}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ...">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    @if ($qtinfo == null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    @if($result == 'notfound')
                        <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                        <small>Rekod tidak dijumpai!</small>
                    @else
                        @if($type == 'qtno')
                            <i class="gi gi-search"></i>Carian <PERSON>at/Lawatan Tapak (Quotation/Tender)<br>
                            <small>Masukkan QT No. pada carian diatas...</small>
                        @endif
                    @endif
                </h1>
            </div>
        </div>
    @endif
    @if($qtinfo)
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title  panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Quotation/Tender : {{ $qtinfo->qt_no }}</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-9">
                        <h6><strong>{{ $qtinfo->qt_title }}</strong></h6>
                        <address>
                            <strong>QT No.</strong> : {{ $qtinfo->qt_no }}<br />
                            <strong>File No.</strong> : {{ $qtinfo->file_no }}<br />
                            <strong>Publish Date</strong> : {{ $qtinfo->publish_date }}<br />
                            <strong>Proposal Start Date</strong> : {{ $qtinfo->proposal_start_date }}<br />
                            <strong>Closing Date</strong> : {{ $qtinfo->closing_date }}<br />
                            <strong>BSV Date</strong> : {{ $qtinfo->bsv_date }}<br />
                        </address>
                    </div>
                    <div class="col-md-3">
                        <div class="table-responsive">
                            <table id="general-table" class="table table-vcenter table-hover table-borderless">
                                <tbody>
                                <tr>
                                    <td class="text-center"><span class="badge label-danger"><strong>B</strong></span></td>
                                    <td class="text-left">Failed to attend briefing and site visit</td>
                                </tr>
                                <tr>
                                    <td class="text-center"><span class="badge label-danger"><strong>C</strong></span></td>
                                    <td class="text-left">Supplier Criteria</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row">
                    @if($listdata)
                        <div class="table-responsive">
                            <table id="attend-list" class="table table-striped table-vcenter">
                                <thead>
                                <tr>
                                    <th class="text-center">No.</th>
                                    <th class="text-center">MOF No.</th>
                                    <th class="text-center">Identification No.</th>
                                    <th class="text-left">Name</th>
                                    {{--<th class="text-left">Email</th>--}}
                                    <th class="text-center">Authorized</th>
                                    <th class="text-center">Pre Registered</th>
                                    <th class="text-center">Attended</th>
                                    <th class="text-center">Post Registered</th>
                                    <th class="text-center">Disqualified Stage</th>
                                    {{--<th class="text-center">QT BSV Reg Status</th>--}}
                                    <th class="text-center">Approval Status</th>
                                    <th class="text-center">Info</th>
                                </tr>
                                </thead>

                                <tbody>
                                @foreach ($listdata as  $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ ++$indexKey }}</td>
                                        <td class="text-center">{{ $data->mof_no }}</td>
                                        <td class="text-center">{{ $data->ic_passport }}</td>
                                        <td class="text-left">{{ $data->name }}</td>
                                        {{--<td class="text-left">{{ $data->email }}</td>--}}
                                        <td class="text-center">{!! $data->is_authorized !!}</td>
                                        <td class="text-center">{!! $data->is_pre_registered !!}</td>
                                        <td class="text-center">{!! $data->is_attended !!}</td>
                                        <td class="text-center">{!! $data->is_post_registered !!}</td>
                                        <td class="text-center">{!! $data->qualify->disqualified_stage !!}</td>
                                        {{--<td class="text-center">{{ $data->record_status }}</td>--}}
                                        <td class="text-center">{{ $data->approval_status }}</td>
                                        <td class="text-center">
                                            <a href='#modal-list-data'
                                               class='modal-list-data-action btn btn-xs btn-info'
                                               data-toggle='modal'
                                               data-url='/list/qt/detail/{{ $data->supplier_id }}/{{ $data->qt_no }}/{{ $data->qt_bsv_attendance_id }}/'
                                               data-title='{{ $data->name }}'>
                                                <i class="fa fa-info"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div id="qt-detail"></div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>

@endsection

@section('jsprivate')
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        App.datatables();
        /* Initialize Datatables */
        var tableListData = $('#attend-list').DataTable({
            columnDefs: [{orderable: false, targets: [0]}],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        });

        $(document).ready(function () {

            $('.table-responsive').on("click", '.modal-list-data-action', function () {

                $('.spinner-loading').show();
                $('#qt-detail').html('').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data)
                        $('.spinner-loading').hide();
                        $('#qt-detail').html($data).fadeIn();
                    }
                });

            });
        });
    </script>
@endsection