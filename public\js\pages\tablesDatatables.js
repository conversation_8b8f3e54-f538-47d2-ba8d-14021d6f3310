/*
 *  Document   : tablesDatatables.js
 *  Author     : pixelcave
 *  Description: Custom javascript code used in Tables Datatables page
 */

var TablesDatatables = function() {

    return {
        init: function() {
            /* Initialize Bootstrap Datatables Integration */
            App.datatables();

            /* Initialize Datatables */
            $('#basic-datatable').dataTable({
                columnDefs: [  ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });
            
             /* Initialize Datatables */
            $('#tracking-datatable').dataTable({
                order: [[ 3, "desc" ]],
                columnDefs: [  ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            
            $('#ws-datatable').dataTable({
                order: [[ 3, "desc" ]],
                columnDefs: [  ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            
            $('#bpm-datatable').dataTable({
                order: [[ 0, "desc" ]],
                columnDefs: [  ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            
            /* Initialize Datatables */
            $('#datatable-category-code').dataTable({
                columnDefs: [ { orderable: false, targets: [ 1, 5 ] } ],
                pageLength: 5,
                lengthMenu: [[5, 10, 20, 30, -1], [5, 10, 20, 30, 'All']]
            });

            $('#item-codification-datatable').DataTable( {
                dom: 'Bfrtip',
                buttons: [
                     'excel', 'pdf'
                ],
                order: [[ 0, "desc" ]],
                columnDefs: [  ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            } );


            /* Add placeholder attribute to the search input */
            $('.dataTables_filter input').attr('placeholder', 'Search');
        }
    };
}();