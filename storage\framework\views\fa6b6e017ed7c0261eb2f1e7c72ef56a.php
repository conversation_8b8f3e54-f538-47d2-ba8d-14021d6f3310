<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/uom')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

    <?php if($listdata == null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <?php if($result == 'notfound'): ?>
                        <i class="gi gi-ban"></i>Carian: <?php echo e($carian); ?><br>
                        <small>Rekod tidak dijumpai!</small>
                    <?php else: ?>
                        <i class="gi gi-search"></i>Carian UOM<br>
                        <small>Masukkan UOM pada carian diatas... (Contoh: gram, unit..dll)</small>
                    <?php endif; ?>
                </h1>
            </div>
        </div>
    <?php endif; ?>

    <?php if($listdata != null): ?>
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Carian UOM<br>
                    <small>Masukkan UOM pada carian diatas... (Contoh: gram, unit..dll)</small>
                </h1>
            </div>
        </div>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai UOM </strong>
                        <small>Hasil Carian : <?php echo e($carian); ?></small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">CODE</th>
                            <th class="text-center">NAME</th>
                            <th class="text-center">DECIMAL SCALE</th>
                            <th class="text-center">CREATED DATE</th>
                            <th class="text-center">CHANGED DATE</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-center"><?php echo e($data->uom_id); ?></td>
                                <td class="text-center"><?php echo e($data->uom_code); ?></td>
                                <td class="text-center"><?php echo e($data->uom_name); ?></td>
                                <td class="text-center"><?php echo e($data->decimal_scale); ?></td>
                                <td class="text-center"><?php echo e($data->created_date); ?></td>
                                <td class="text-center"><?php echo e($data->changed_date); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    <?php endif; ?>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_uom.blade.php ENDPATH**/ ?>