<?php
/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 3/14/2018
 * Time: 12:32 PM
 */

namespace App\Http\Controllers;

use App\Services\Traits\OrganizationService;


class EpOrganizationController extends Controller
{
    use OrganizationService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function searchByOrgCode($orgCode)
    {
        $org_code_type = 'org_code';
        $data = $this->getPmOrganization($org_code_type,$orgCode);
        $this->setPmOrgHierarchy($data);

        if (count($data) > 0) {
            $orgProfileId =  $data[0]->org_profile_id;
            $org_profile_type = 'org_profile_id';
            $list = $this->getDetailUserList($org_profile_type, $orgProfileId);

            return view('user_details_orgcode', [
                'orginfo' => $data,
                'listdata' => $list,
                'carian' => $orgCode,
                'type' => 'orgcode']);
        }
        return view('user_details_orgcode', [
            'orginfo' => null,
            'listdata' => null,
            'result' => 'notfound',
            'carian' => $orgCode,
            'type' => 'orgcode']);

    }

    public function searchByIdentificationNo($icNo)
    {
        $type = 'identification_no';
        $list = $this->getDetailUserList($type,$icNo);
     
        if (count($list) > 0) {
            
            //Detect Found as Supplier User
            if($list[0]->org_type_id == 15){
                return redirect('/find/icno/'.$icNo);
            }
            
            $orgProfileId =  $list[0]->org_profile_id;
            $type = 'org_profile_id';
            $orginfo = $this->getPmOrganization($type,$orgProfileId);
            $this->setPmOrgHierarchy($orginfo);

            return view('user_details_orgcode', [
                'orginfo' => $orginfo,
                'listdata' => $list,
                'carian' => $icNo,
                'type' => 'icno']);
        }
        return view('user_details_orgcode', [
            'orginfo' => null,
            'listdata' => null,
            'result' => 'notfound',
            'carian' => $icNo,
            'type' => 'icno']);
    }


    public function getDetailUserList($type,$value)
    {
       $list = $this->getUserList($type,$value);
        if (count($list) > 0) {
            foreach ($list as $data) {
                $listUserRole = $this->getUserRole($data->user_id);
                $isAdmin = '';
                foreach($listUserRole as $userRole){
                    $roleDescBM = $this->getRoleDesc($userRole->role_code, 'ms') ;
                    if($roleDescBM) {
                        $userRole->role_name =  $userRole->role_name. ' , '.$roleDescBM->role_name;
                    }

                    if($userRole->role_code == 'PTJ_ADMIN' 
                           || $userRole->role_code == 'MINISTRY_ADMIN' 
                           || $userRole->role_code == 'JABATAN_ADMIN' ){
                       $isAdmin = $roleDescBM->role_code. ' , '.$roleDescBM->role_name;
                    }
                }
                
                
                $data->role_admin = $isAdmin;
                

                $data->listUserRole = $listUserRole;

                $listApprover = $this->getPmUserGroupByUserId($data->user_id);
                $data->listApprover = $listApprover;
            }
        }
        return $list; 
    }
    

    /**
     * Set hierarchy for Org Gov profile.  KEMENTERIAN >> PEGAWAI PENGAWAL >> KUMPULAN PTJ >> PTJ
     * @param type $data
     * @return type
     */
    public function setPmOrgHierarchy(&$data)
    {
        if (count($data) > 0) {
            if ($data[0]->parent_org_profile_id) {
                $orgProfileId = $data[0]->parent_org_profile_id;
                $hierarchy = array();
                for ($x = 0; $x < $data[0]->org_type_id - 2; $x++) {
                    if ($x == 0) {
                        ${"id" . $x} = $this->getPmOrgValidityByParentId($orgProfileId);
                    } else {
                        ${"id" . $x} = $this->getPmOrgValidityByParentId(${"id" . ($x - 1)}[0]->parent_org_profile_id);
                    }
                    $hierarchy[] = array('org_name' => ${"id" . $x}[0]->org_name, 'org_code' => ${"id" . $x}[0]->org_code,'org_type' => ${"id" . $x}[0]->code_desc );
                }
                $data[0]->hierarchy = array_reverse($hierarchy);
            } else {
                $data[0]->hierarchy = null;
            }
        }
        return $data;
    }

    public function searchByOrgCodeDefault()
    {
        return view('user_details_orgcode', [
            'orginfo' => null,
            'type' => 'orgcode',
            'result' => null,
            'carian' => '']);
    }

    public function searchByIdentificationNoDefault()
    {
        return view('user_details_orgcode', [
            'orginfo' => null,
            'type' => 'icno',
            'result' => null,
            'carian' => '']);
    }
}