<?php if($dataFired): ?>
    <div class="widget-extra themed-background-dark">
        <div class="widget-content-light">
            <span data-toggle="tooltip" title="Trigger State" class="label <?php echo e($dataFired[0]->state_color); ?> pull-right"><?php echo e($dataFired[0]->trigger_state); ?></span>
            <h5>Quartz <strong>Log</strong></h5>
        </div>
    </div>
    <!-- END Dashboard Info Title -->

    <!-- Dashboard Info -->
    <table class="table table-borderless table-striped table-vcenter">
        <tbody>
        <tr>
            <td class="text-right" style=""><strong>Service Code</strong></td>
            <td><strong><?php echo e($dataFired[0]->job_group); ?></strong></td>
            <td class="text-right" style=""><strong>Transaction Type</strong></td>
            <td><?php echo e($dataFired[0]->job_name); ?></td>
        </tr>
        <tr>
            <td class="text-right"><strong>Previous Fired</strong></td>
            <td><?php echo e($dataFired[0]->prev_fired); ?></td>
            <td class="text-right"><strong>Next Fire</strong></td>
            <td><?php echo e($dataFired[0]->next_fired); ?></td>
        </tr>
        <?php if($dataExecution): ?>
            <tr>
                <td class="text-right"><strong>Finish Execution Date</strong></td>
                <td><?php echo e($dataExecution[0]->finish_execution_date); ?></td>
                <td class="text-right"><strong>Duration</strong></td>
                <td><?php echo e($dataExecution[0]->duration); ?></td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>
<?php endif; ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\include\mminf_dash_quartz.blade.php ENDPATH**/ ?>