@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')

    @if($data == null)
    <div class="alert alert-success alert-dismissable">
        <h4><i class="fa fa-close"></i> Record not found </h4> Something wrong with data<a href="javascript:void(0)" class="alert-link"></a>
    </div>
    @endif
    
    @if(isset($data) && $data != null )
    
    <div class="block">
        <div class="block-title">
            <h2><strong>Kemaskini</strong> Pengg<PERSON> Pembekal</h2>
            <small class="text-primary">connect to :  {{env('DB_NEXTGEN_FULLGRANT_DATABASE')}}</small>
            <div class="block-options pull-right action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="{{url('/support/report/log/patch-data')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                   data-title="List Action Patch Data Today ">View Today Action</a>
            </div>
        </div>
        
        @if(isset($result_status) && $result_status == 'success')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Success</h4> {{$result_desc}}</a>
        </div>
        @elseif(isset($result_status) && $result_status == 'failed')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Failed</h4> Task <a href="javascript:void(0)" class="alert-link">failed</a>
        </div>
        @endif
        <ul>
            <ol>
        <span class="text-primary"><strong>Segala aktiviti kemaskini yang dilakukan akan disimpan. Sila pastikan anda meletakkan 
            sebab data patching di ruangan 'Remarks'. Masukkan juga Case Number. </strong></span>
            </ol>
        </ul>    
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <form id="form-search-mminf" action="{{url("/find/userpersonnel")}}/{{ $data->appl_id  }}/{{ $data->personnel_id  }}" method="post" class="form-horizontal" onsubmit="return true;">
            {{ csrf_field() }}
            <input name="_method" id="_method" type="hidden" value="POST">
            <span id="selection_process_list" class="hide" >{{json_encode(App\Services\EPService::$USER_PROCESS_PATCHING)}}</span>
            <div class="form-group">
                <label class="col-md-3 control-label" for="selected_process_id">Patching <span class="text-danger">*</span></label>
                <div class="col-md-6">
                    <div class="input-group">
                        <select id="selected_process_id" name="selected_process_id" class="form-control">
                            <option value="">Please select</option>
                            @foreach(App\Services\EPService::$USER_PROCESS_PATCHING as  $key => $obj)
                            <option value="{{$key}}" @if(old('status_id') == $key) selected @endif>{{$obj['name']}}</option>
                            @endforeach
                        </select>
                        <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                    </div>
                    <span class="text-info" id="selected_process_id_desc"></span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label" for="remarks">Remarks / Case Number <span class="text-danger">*</span></label>
                <div class="col-md-6">
                    <input type="text" id="remarks" name="remarks" class="form-control" >
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Kemaskini</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
        </form>
    </div>
    
    <div class="block block-alt-noborder full">
        <div class="block-title">
            <h2><strong>Maklumat Pengguna Pembekal</strong>  {{ $data->company_name }} &raquo;  <a href="{{url('find/epno/')}}/{{ $data->ep_no }}">{{ $data->ep_no }}</a></h2>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>Maklumat User Login  </h2>
                    </div>
                    <address class="text-left">
                        <strong>User ID </strong> : {{ $data->user_id  }}<br />
                        <strong>Login ID </strong> : {{ $data->login_id  }}<br />
                        <strong>Email </strong> : {{ $data->email  }}<br />
                        <strong>ICNO </strong> : {{ $data->identification_no  }}<br />
                        <strong>Mobile </strong> : {{ $data->mobile_country }}{{ $data->mobile_area }}{{ $data->mobile_no }}<br />
                        <strong>Record Status </strong> : {{ $data->u_record_status }}<br />
                        <strong>Last Login Date </strong> : {{ $data->login_date }}<br />
                        <strong>Last Date Changed </strong> :  {{  $data->changed_date }} <br />

                        <br />
                        <strong>Peranan</strong> : <br/>
                        @if($data->roles && count($data->roles) > 0 )
                            @foreach ($data->roles as $role)
                                {{ $role->role_code }} <br/>
                            @endforeach
                        @else
                        Tiada
                        @endif
                    </address>
                </div>
            </div>

            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>Maklumat Diri  Staf Syarikat </h2>
                    </div>
                    <address  class="text-left">
                        <strong>Personal ID </strong> : {{ $data->personnel_id  }}<br />
                        <strong>Appl ID </strong> : {{ $data->appl_id  }}<br />
                        <strong>Designation </strong> : {{ $data->p_designation  }}<br />
                        <strong>Email </strong> : {{ $data->p_email  }}<br />
                        <strong>Role </strong> : {{ $data->p_ep_role }}<br />
                        <strong>Mobile </strong> : {{ $data->p_mobile_country }}{{ $data->p_mobile_area }}{{ $data->p_mobile_no }}<br />
                        <strong>SoftCert Status </strong> : {{ $data->p_is_softcert  }}<br />
                        <strong>Is Equity Owner? </strong> : {{ $data->is_equity_owner  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Is Contract Signer? </strong> : {{ $data->is_contract_signer  }}<br />
                        <strong>Is Authorized? </strong> : {{ $data->is_authorized  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Is Contact Person? </strong> : {{ $data->is_contact_person  }}<br />
                        <strong>Is Management? </strong> : {{ $data->is_mgt  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Is Director? </strong> : {{ $data->is_director  }}<br />
                        <strong>Is Bumi? </strong> : {{ $data->is_bumi  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Record Status </strong> : {{ $data->p_record_status }}<br />
                        <strong>Last Date Changed </strong> :  {{  $data->p_changed_date }} <br />
                        <strong>Rev. No. </strong> :  {{  $data->p_rev_no }} <br />
                    </address>
                </div>
            </div>

            @if(count($data->listSoftCert ) > 0)
            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>SoftCert Request</h2>
                    </div>

                    @foreach ($data->listSoftCert as $sCert)
                    <address  class="text-left softcert">
                        <strong>SoftCert Request ID </strong> : {{ $sCert->softcert_request_id  }}<br /> 
                        @if($sCert->is_trustgate == true)
                        <a target='_blank'
                           style="color:darkblue;font-weight: bolder;text-decoration: underline;"
                           href="https://digitalid.msctrustgate.com/nextgenEP/ePRA/migrate/detail_migrate?epNo={{$sCert->ep_no}}&icNo={{$data->p_identification_no}}" >
                            Check TrustGate (@if($sCert->is_trustgate_data == true)Expiry on  {{$sCert->trustgate_expired_date}}@endif)</a><br />
                        @endif
                        <strong>Record Status </strong> : {{ $sCert->record_status }}<br />
                        <strong>Date Created </strong> :  {{  $sCert->created_date }} <br />
                        <strong>Last Date Changed </strong> :  {{  $sCert->changed_date }} <br />
                        <strong>Using Free SoftCert</strong> :  {{  $sCert->is_free }} <br />
                        
                        @if(strlen($sCert->cert_serial_no)>0)
                        <strong>Cert Serial NO. </strong> : {{ $sCert->cert_serial_no  }}<br />
                        <strong>Cert Issuer </strong> : @if($sCert->cert_issuer=='T') TrustGate @else Digicert @endif<br />
                        <strong>Cert Valid From </strong> : {{ $sCert->valid_from  }}<br />
                        <strong>Cert Valid To</strong> : {{ $sCert->valid_to  }}<br />
                        <strong>Cert Record Status </strong> : {{ $sCert->pdc_record_status }}<br />
                        <strong>Cert Last Date Changed </strong> :  {{  $sCert->pdc_changed_date }} <br />
                        @else

                            @if($sCert->is_success_SPK020 > 0)
                                <a href='#modal-osbdetail-spki'
                                   class='modal-list-data-action'
                                   data-toggle='modal'
                                   data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-020/'
                                   data-title='Sent Request to SPKI Detail'>
                                    <strong style="color:forestgreen;">Successful sent request to SPKI</strong><br />
                                </a>
                            @endif
                            @if($sCert->is_success_SPK010 > 0)
                                <a href='#modal-osbdetail-spki'
                                   class='modal-list-data-action'
                                   data-toggle='modal'
                                   data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-010/'
                                   data-title='Received Update from SPKI Detail'>
                                    <strong style="color:forestgreen;">Successfully received update from SPKI</strong><br />
                                </a>
                            <span style="font-style: italic">{{ $sCert->remark }}</span><br />
                            @endif

                        <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />

                        @endif
                    </address>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>    
    @endif
    
    @if(isset($data_inprogress) && $data_inprogress != null )
    <div class="block block-alt-noborder full">
        <div class="block-title">
            <h2><strong>Maklumat Pengguna Pembekal (In Progress Application : KU-14022018-0015 )</strong>  {{ $data_inprogress->company_name }} &raquo;  <a href="{{url('find/epno/')}}/{{ $data_inprogress->ep_no }}">{{ $data_inprogress->ep_no }}</a></h2>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>Maklumat User Login  </h2>
                    </div>
                    <address class="text-left">
                        <strong>User ID </strong> : {{ $data_inprogress->user_id  }}<br />
                        <strong>Login ID </strong> : {{ $data_inprogress->login_id  }}<br />
                        <strong>Email </strong> : {{ $data_inprogress->email  }}<br />
                        <strong>ICNO </strong> : {{ $data_inprogress->identification_no  }}<br />
                        <strong>Mobile </strong> : {{ $data_inprogress->mobile_country }}{{ $data_inprogress->mobile_area }}{{ $data_inprogress->mobile_no }}<br />
                        <strong>Record Status </strong> : {{ $data_inprogress->u_record_status }}<br />
                        <strong>Last Login Date </strong> : {{ $data_inprogress->login_date }}<br />
                        <strong>Last Date Changed </strong> :  {{  $data_inprogress->changed_date }} <br />

                        <br />
                        <strong>Peranan</strong> : <br/>
                        @if($data_inprogress->roles && count($data_inprogress->roles) > 0 )
                            @foreach ($data_inprogress->roles as $role)
                                {{ $role->role_code }} <br/>
                            @endforeach
                        @else
                        Tiada
                        @endif
                    </address>
                </div>
            </div>

            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>Maklumat Diri  Staf Syarikat </h2>
                    </div>
                    <address  class="text-left">
                        <strong>Personal ID </strong> : {{ $data_inprogress->personnel_id  }}<br />
                        <strong>Appl ID </strong> : {{ $data_inprogress->appl_id  }}<br />
                        <strong>Designation </strong> : {{ $data_inprogress->p_designation  }}<br />
                        <strong>Email </strong> : {{ $data_inprogress->p_email  }}<br />
                        <strong>Role </strong> : {{ $data_inprogress->p_ep_role }}<br />
                        <strong>Mobile </strong> : {{ $data_inprogress->p_mobile_country }}{{ $data_inprogress->p_mobile_area }}{{ $data_inprogress->p_mobile_no }}<br />
                        <strong>SoftCert Status </strong> : {{ $data_inprogress->p_is_softcert  }}<br />
                        <strong>Is Equity Owner? </strong> : {{ $data_inprogress->is_equity_owner  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Is Contract Signer? </strong> : {{ $data_inprogress->is_contract_signer  }}<br />
                        <strong>Is Authorized? </strong> : {{ $data_inprogress->is_authorized  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Is Contact Person? </strong> : {{ $data_inprogress->is_contact_person  }}<br />
                        <strong>Is Management? </strong> : {{ $data_inprogress->is_mgt  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Is Director? </strong> : {{ $data_inprogress->is_director  }}<br />
                        <strong>Is Bumi? </strong> : {{ $data_inprogress->is_bumi  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                        <strong>Record Status </strong> : {{ $data_inprogress->p_record_status }}<br />
                        <strong>Last Date Changed </strong> :  {{  $data_inprogress->p_changed_date }} <br />
                        <strong>Rev. No. </strong> :  {{  $data_inprogress->p_rev_no }} <br />
                    </address>
                </div>
            </div>

            @if(count($data_inprogress->listSoftCert ) > 0)
            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>SoftCert Request</h2>
                    </div>

                    @foreach ($data_inprogress->listSoftCert as $sCert)
                    <address  class="text-left softcert">
                        <strong>SoftCert Request ID </strong> : {{ $sCert->softcert_request_id  }}<br /> 
                        @if($sCert->is_trustgate == true)
                        <a target='_blank'
                           style="color:darkblue;font-weight: bolder;text-decoration: underline;"
                           href="https://digitalid.msctrustgate.com/nextgenEP/ePRA/migrate/detail_migrate?epNo={{$sCert->ep_no}}&icNo={{$data_inprogress->p_identification_no}}" >
                            Check TrustGate (@if($sCert->is_trustgate_data == true)Expiry on  {{$sCert->trustgate_expired_date}}@endif)</a><br />
                        @endif
                        <strong>Record Status </strong> : {{ $sCert->record_status }}<br />
                        <strong>Date Created </strong> :  {{  $sCert->created_date }} <br />
                        <strong>Last Date Changed </strong> :  {{  $sCert->changed_date }} <br />
                        <strong>Using Free SoftCert</strong> :  {{  $sCert->is_free }} <br />
                        
                        @if(strlen($sCert->cert_serial_no)>0)
                        <strong>Cert Serial NO. </strong> : {{ $sCert->cert_serial_no  }}<br />
                        <strong>Cert Issuer </strong> : @if($sCert->cert_issuer=='T') TrustGate @else Digicert @endif<br />
                        <strong>Cert Valid From </strong> : {{ $sCert->valid_from  }}<br />
                        <strong>Cert Valid To</strong> : {{ $sCert->valid_to  }}<br />
                        <strong>Cert Record Status </strong> : {{ $sCert->pdc_record_status }}<br />
                        <strong>Cert Last Date Changed </strong> :  {{  $sCert->pdc_changed_date }} <br />
                        @else

                            @if($sCert->is_success_SPK020 > 0)
                                <a href='#modal-osbdetail-spki'
                                   class='modal-list-data-action'
                                   data-toggle='modal'
                                   data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-020/'
                                   data-title='Sent Request to SPKI Detail'>
                                    <strong style="color:forestgreen;">Successful sent request to SPKI</strong><br />
                                </a>
                            @endif
                            @if($sCert->is_success_SPK010 > 0)
                                <a href='#modal-osbdetail-spki'
                                   class='modal-list-data-action'
                                   data-toggle='modal'
                                   data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-010/'
                                   data-title='Received Update from SPKI Detail'>
                                    <strong style="color:forestgreen;">Successfully received update from SPKI</strong><br />
                                </a>
                            <span style="font-style: italic">{{ $sCert->remark }}</span><br />
                            @endif

                        <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />

                        @endif
                    </address>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div> 
    @endif
    
    @include('_shared._modalListLogAction')
    
@endsection

@section('jsprivate')
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
    <script>

        $( "#selected_process_id" ).bind( "change", function() {
            var processList = JSON.parse($("#selection_process_list" ).text());
            //console.log(processList);
            var processId = $(this).find(":selected").val();
            //console.log('processId',processId);
            if(processId.length > 0){
                var processObj = processList[processId];
                $('#selected_process_id_desc').text(processObj.description);
                //console.log(processObj);
            }
        });
    
        //Confirm Trigger Dialogue
        $('td.action_table').on("click",'a.action_trigger', function(){
            var ePno = $(this).attr('data-id');
            var changedDate = $("#current_changed_date").val();
            console.log("eP No.: "+ePno);
            console.log("changedDate: "+changedDate);
            $("#trigger_epno").val(ePno);
            $("#trigger_epno_display").text(ePno);
        });
        $('div.form-actions').on("click",'button.action_confirm_trigger', function(){
            var ePno = $("#trigger_epno").val();
            var changedDate = $("#current_changed_date").val();
            var supplierId = $("#supplier_id").val();
            var csrf = $("input[name=_token]").val();
            console.log("eP No.: "+ePno);
            console.log("changedDate: "+changedDate);

            $('#modal_confirm_trigger').modal('hide');
            $('#wait-modal').modal('toggle');

            $.ajax({
                url: "/trigger/gfmas/apive/update",
                method : "POST",
                dataType : "json",
                data : {"_token":csrf,"ep_no":ePno,"changed_date":changedDate},
                context: document.body
            }).done(function(resp) {
                if(resp.status === 'success'){
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Successfully Triggered!");
                    $("td.td-"+supplierId).addClass("text-success");
                    $("td.td-"+supplierId).html(resp.value);
                    $('#wait-modal').modal('hide');
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Trigger Failed! Please try again.");
                    $("td.td-"+supplierId).addClass("text-danger");
                    $('#wait-modal').modal('hide');
                }
            });
        });
    </script>
@endsection