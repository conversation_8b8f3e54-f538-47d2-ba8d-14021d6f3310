<?php

namespace App\Http\Controllers;

use App\Services\Traits\ItemService;
use Carbon\Carbon;

class ItemController extends Controller {

    use ItemService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function searchListItems($carian = null){
        $carianTemp = trim(strtoupper($carian));
        if($carianTemp == null){
            $list = $this->getItemDetail("DEFAULT", '');
            $carianTemp = 'Today Created';
        }else{
            if(strlen($carianTemp) < 4){
                return view('list_item', [
                'listdata' => null,
                'error'   => 'invalid-search',    
                'carian' => '']);
            }

            if(strlen($carianTemp) == 8 && ctype_digit($carianTemp) == true ){
               $list = $this->getItemDetail("UNSPSC_CODE", $carianTemp); 
            }else if(strlen($carianTemp) == 18 ){
               $list = $this->getItemDetail("EXTENSION_CODE", $carianTemp); 
            }else{
               $list = $this->getItemDetail("SEARCH", '%'.$carianTemp.'%');
            }
        }
        
        return view('list_item', [
            'listdata' => $list,
            'carian' => $carianTemp]);
    }
    
    public function searchListUNSPSCItems($carian = null){
        if($carian == null){
            return view('list_item_unspsc', [
            'listdata' => null,
            'carian' => '']);
        }
        
        $carianTemp = trim(strtoupper($carian));
        if(strlen($carianTemp) == 8 && ctype_digit($carianTemp) == true ){
           $list = $this->getUNSPSCItemDetail("UNSPSC_CODE", $carianTemp); 
        }else{
           $list = $this->getUNSPSCItemDetail("UNSPSC_TITLE", '%'.$carianTemp.'%');
        }
        
        
        return view('list_item_unspsc', [
            'listdata' => $list,
            'carian' => $carianTemp]);
    }
    
    public function searchListProductSupplierPendingCodification($carian = null){
        $carianTemp = trim(strtoupper($carian));

        if(strlen($carianTemp) < 4){
            return view('list_item_supplier', [
            'listdata' => null,
            'error'   => 'invalid-search',    
            'carian' => '']);
        }
        
        $subStrCheck = substr($carian, 0, 3);

        if($subStrCheck == '357' || $subStrCheck == '465'){
            $list = $this->getListProductSupplierPendingCodification('MOF_NO', $carianTemp);
        }
        else if($subStrCheck == 'eP-'){
            $list = $this->getListProductSupplierPendingCodification('EP_NO', $carian);
            $carianTemp = $carian;
        }
        else if($subStrCheck == 'RNC' || $subStrCheck == 'RNE'){
            $list = $this->getListProductSupplierPendingCodification('DOC_NO', $carianTemp);
        }
        else {
            $list = $this->getListProductSupplierPendingCodification('LIKE_SEARCH', '%'.$carianTemp.'%');
        }
        

        return view('list_item_supplier', [
            'listdata' => $list,
            'carian' => $carianTemp]);
    }
    
    public function searchListProductSupplierCodificationTask($carian = null){
        
        $carianTemp = trim(strtoupper($carian));
        //dd(request()->all());
        
        $carbonNow = Carbon::now();
        $tarikhSearch = $carbonNow->format('Y-m-d');
        if (request()->isMethod('post')) {
            $tarikhSearch = request()->tarikh;
            $carianTemp = request()->cari;
        }
        

        $subStrCheck = substr(request()->cari, 0, 3);
        

        if($subStrCheck == 'RNC' || $subStrCheck == 'RNE'){
            $list = $this->getListProductSupplierCodificationTask('DOC_NO', $carianTemp,$tarikhSearch);
        }
        else {
            $list = $this->getListProductSupplierCodificationTask('LIKE_SEARCH', '%'.$carianTemp.'%',$tarikhSearch);
        }
        

        session()->flashInput(request()->input());
        return view('list_item_codi_task', [
            'listdata' => $list,
            'carian' => $carianTemp]);
    }

    
    public function searchListItemTypeByUNSPSCID($carian){
        $list = $this->getListItemTypeByUNSPSCID($carian);
        return $this->populateListTableHTML($list);
    }
    
    public function searchListItemBrandByUNSPSCID($carian){
        $list = $this->getListItemBrandByUNSPSCID($carian);
        return $this->populateListTableHTML($list);
    }
    
    public function searchListItemMeasurementByUNSPSCID($carian){
        $list = $this->getListItemMeasurementByUNSPSCID($carian);
        return $this->populateListTableHTML($list);
    }
    
    public function searchListItemColor(){
        $list = $this->getListItemColor();
        return $this->populateListTableHTML($list);
    }
    
    
    protected function populateListTableHTML($dataList){
        
        $html =    "<thead>
                        <tr>
                            <th class='text-center'>ID</th>
                            <th class='text-center'>CODE</th>
                            <th class='text-center'>NAME</th>
                        </tr>
                    </thead>";
        $html = $html."<tbody>";
        foreach ($dataList as $data){
                $data = "
                    <tr>
                        <td class='text-center'>$data->id</td>
                        <td class='text-left'>$data->code</td>
                        <td class='text-left'>$data->name</td>
                    </tr>";
                $html = $html.$data;
        }
        $html = $html."<tbody>";
        return $html;
    }

}
