<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use SSH;

class FulfilmentController extends Controller {

    use SupplierService;
    use OSBService;
    use ProfileService;
    use FulfilmentService;


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function transactionDocNo() {
        return view('list_doc_transaction', [
            'listdata' => null,
            'listXml' => null,
            'carian' => '']);
    }

    public function searchTransactionDocNo($search) {
        if($search == 'insert_docno'){
           return view('list_doc_transaction', [
            'listdata' => array(),
            'carian' => $search
            ]); 
        }
        
        $typeDoc = substr($search, 0, 2);
        $list = array();
        if($typeDoc == 'PD'){
            $list = $this->getListPidByPdNo($search);
        }
        if($typeDoc == 'PI'){
            $list = $this->getListPurchaseInquiryByPiNo($search);
        }
        if($typeDoc == 'SQ'){
            $list = $this->getListSimpleQuoteBySqNo($search);
        }
        if($typeDoc == 'RN'){
            $list = $this->getListRequestNoteByRnNo($search);
        }
        if($typeDoc == 'PR' || $typeDoc == 'CR'){
            $list = $this->getListFulfilmenRequestByPrCr($search);
        }
        if($typeDoc == 'PO' || $typeDoc == 'CO' || $typeDoc == 'FC' || $typeDoc == 'L0' ){
            $list = $this->getListFulfilmenOrderByPoCoFc($search);
        }
        if($typeDoc == 'DO' || $typeDoc == '60'){
            $list = $this->getListDeliveryOrderByDoNo($search);
        }
        if($typeDoc == 'FN'){
            $list = $this->getListFulfilmentNoteByFnNo($search);
        }
        if($typeDoc == 'SD'){
            $list = $this->getListStopInstructionBySdNo($search);
        }
        if($typeDoc == '60'){
            $listInv = $this->getListInvoiceByInvNo($search);
            foreach($listInv as $data){
                array_push($list,$data);
            }
        }
        if($typeDoc == 'C0' || $typeDoc == 'B0'){
            $list = $this->getListAdjustmentByDocNo($search);
        }
        if($typeDoc == 'PA'){
            $list = $this->getListPaymentAdviseByPaNo($search);
        }
        
        $supplier = null;
        if($typeDoc == 'PR' || $typeDoc == 'CR' || $typeDoc == 'PO' || $typeDoc == 'CO'){
           $supplier = $this->getSupplierInfoByDocNo($search); 
        }
        
        return view('list_doc_transaction', [
            'listdata' => $list,
            'supplier' => $supplier,
            'carian' => $search
            ]);
    }
    
    public function getListDocNoTrackingDefault(){
        return view('list_doc_tracking', [
                'listdata' => array(),
                'carian' => null
            ]);
    }
    
    public function getListDocNoTracking($docNo) {
        
        //Search Default SQ to PO
        $list = $this->getListDocNoFulfillmentSQPOTracking($docNo);
        $searchType = 1;
        
        //Search Default SQ to CO
        if(count($list) == 0){
            $list = $this->getListDocNoFulfillmentSQCOTracking($docNo);
            $searchType = 2;
        }
        
        //Searching for QT to CO only
        if(count($list) == 0){
            $list = $this->getListDocNoFulfillmentQTCOTracking($docNo);
            $searchType = 3;
        }
        
        //Searching for PRCR POCO only
        if(count($list) == 0){
            $list = $this->getListDocNoFulfillmentPRCRPOCOTracking($docNo);
            $searchType = 4;
        }
        
        //Searching for LOA CT POCO only
        if(count($list) == 0){
            $list = $this->getListDocNoFulfillmentLOACTCRCOTracking($docNo);
            $searchType = 5;
        }
        
        //Filter > Do not proceed in tracking diary if result more than 10. If will suffer the processing query data.
        if(count($list) > 10 ){
            return 'This Document No. skip to process. Try search other document no related.';
        }
        
        $listDocNo = array();
        $listGroupIdTracking = array();
        if(count($list) > 0){
            
            if(isset($list[0]->quote_no) && strlen($list[0]->quote_no) > 0){
                array_push($listDocNo, $list[0]->quote_no);
            }
            
            if(isset($list[0]->request_note_no) && strlen($list[0]->request_note_no) > 0){
                array_push($listDocNo, $list[0]->request_note_no);
            }
            
            if(isset($list[0]->loa_doc_no) && strlen($list[0]->loa_doc_no) > 0){
                array_push($listDocNo, $list[0]->loa_doc_no);
            }
            
            if(isset($list[0]->ct_doc_no) && strlen($list[0]->ct_doc_no) > 0){
                array_push($listDocNo, $list[0]->ct_doc_no);
            }
            
            if(isset($list[0]->qt_doc_no) && strlen($list[0]->qt_doc_no) > 0){
                array_push($listDocNo, $list[0]->qt_doc_no);
            }
            
            foreach ($list as $obj){
                if($obj->fr_doc_no && strlen($obj->fr_doc_no) > 0){
                    array_push($listDocNo, $obj->fr_doc_no);
                    $objResult = $this->getGroupIdTrackingDiary($obj->fr_doc_no);
                    if($objResult != null && $objResult->group_id != null){
                        array_push($listGroupIdTracking, $objResult->group_id);
                    }
                }
            }
        }
        
       /**
        * If not found step 1 search, So just search in Tracking Diary
        */
       if(count($listDocNo) == 0 && count($listGroupIdTracking) == 0){
           array_push($listDocNo, $docNo);
           $objResult = $this->getGroupIdTrackingDiary($docNo);
           if($objResult != null && $objResult->group_id != null){
               array_push($listGroupIdTracking, $objResult->group_id);
           }
       }

       $collect = collect([]);

       $collect->put('list_doc_no', $listDocNo);
       $collect->put('list_group_id',$listGroupIdTracking); 

       $listResult = $this->getListTrackingDiary($collect);

       
        $supplier = null;
        $typeDoc = substr($docNo, 0, 2);
        if($typeDoc == 'PR' || $typeDoc == 'CR' || $typeDoc == 'PO' || $typeDoc == 'CO'){
           $supplier = $this->getSupplierInfoByDocNo($docNo); 
        }
       if(count($listResult) > 0){
            return view('list_doc_tracking', [
                'listdata' => $listResult,
                'supplier' => $supplier,
                'carian' => $docNo
            ]);
       }else{ 
            return view('list_doc_tracking', [
                'listdata' => array(),
                'supplier' => $supplier,
                'carian' => $docNo
            ]);
       }
       
    }
    
    
    

}
