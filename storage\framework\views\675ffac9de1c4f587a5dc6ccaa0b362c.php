<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form id="carianform" action="<?php echo e(url('/find/bpm/task/docno')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="<?php echo e($carian); ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>


    
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Tugasan BPM<br>
                <small>Carian Dokumen No.</small>
            </h1>
        </div>
    </div>
    <?php if($listdata != null): ?>
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading" style="background-color: #FCFA66;">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Tugasan </strong>
                        <small>Hasil Carian : <?php echo e($carian); ?></small>
                    </h1>
                </div>
                <p class="text-info bolder">Data carian tugasan adalah lewat sehari dari data production.
                <br />
                Data yang ditanda warna <span class="bolder" style="color:chartreuse; "> light green </span> bermaksud tugasan terkini yang ada pada Pengguna.
                </p>
                <div class="table-responsive">
                    <table id="bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">CREATED DATE</th>
                            <th class="text-center">DOC NO.</th>
                            <th class="text-center">TASK</th>
                            <th class="text-center">ACQUIRED BY</th>
                            <th class="text-center">ASSIGNEES</th>
                            <th class="text-center">STATE</th>
                            <th class="text-center">CREATOR</th>
                            <th class="text-center">INSTANCE ID</th>
                            <th class="text-center">COMPOSITE INSTANCE ID</th>
                            <th class="text-center">VERSION REASON</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $listdata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr <?php if($data->state =='ASSIGNED'): ?> style = "background-color:chartreuse;" <?php endif; ?>>
                                <td class="text-center"><?php echo e($data->createddate); ?></td>
                                <td class="text-center"><?php echo e($data->customattributestring1); ?></td>
                                <td class="text-center"><?php echo e($data->activityname); ?></td>
                                <td class="text-center"><?php echo e($data->acquiredby); ?></td>
                                <td class="text-center"><?php echo e($data->assignees); ?></td>
                                <td class="text-center"><?php echo e($data->state); ?></td>
                                <td class="text-center"><?php echo e($data->creator); ?></td>
                                <td class="text-center"><?php echo e($data->instanceid); ?></td>
                                <td class="text-center"><?php echo e($data->compositeinstanceid); ?></td>
                                <td class="text-center"><?php echo e($data->versionreason); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    <?php endif; ?>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layouts.guest-dash', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\workspace\cdccrm-integration-upgrade\resources\views\list_task_bpm.blade.php ENDPATH**/ ?>